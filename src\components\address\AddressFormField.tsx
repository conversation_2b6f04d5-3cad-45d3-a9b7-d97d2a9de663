
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Check, LucideIcon } from 'lucide-react';

interface AddressFormFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  icon?: LucideIcon;
  isValid?: boolean;
  type?: string;
  maxLength?: number;
}

const AddressFormField: React.FC<AddressFormFieldProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder,
  required = false,
  className,
  disabled = false,
  icon: Icon,
  isValid = false,
  type = "text",
  maxLength,
}) => {
  return (
    <div className="mobile-form-group">
      <Label htmlFor={id} className="mobile-label flex items-center gap-2">
        {Icon && <Icon className="h-4 w-4 text-red-500" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <div className="relative">
        <Input
          id={id}
          type={type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          className={`mobile-input bg-white/90 backdrop-blur-sm border-2 rounded-xl transition-all duration-200 touch-feedback ${
            isValid
              ? 'border-green-400 focus:border-green-500 focus:ring-green-500'
              : 'border-gray-200 focus:border-red-500 focus:ring-red-500'
          } ${className}`}
          disabled={disabled}
          maxLength={maxLength}
          style={{ fontSize: '16px' }} // Prevents zoom on iOS
        />
        {isValid && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <Check className="h-5 w-5 text-green-500" />
          </div>
        )}
      </div>
    </div>
  );
};

export default AddressFormField;
