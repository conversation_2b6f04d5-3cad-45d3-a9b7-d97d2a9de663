import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, UserRole, BulkRoleAssignment } from '@/types';
import { validateBulkRoleAssignment, formatSingleRole, hasPermission } from '@/utils/roleUtils';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users, Upload, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface BulkRoleAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  users: User[];
  onAssignRoles: (assignment: BulkRoleAssignment) => Promise<void>;
}

export const BulkRoleAssignmentDialog: React.FC<BulkRoleAssignmentDialogProps> = ({
  isOpen,
  onClose,
  users,
  onAssignRoles
}) => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();
  
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [targetRole, setTargetRole] = useState<UserRole | ''>('');
  const [removeExistingRoles, setRemoveExistingRoles] = useState(false);
  const [reason, setReason] = useState('');
  const [expiresAt, setExpiresAt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [csvInput, setCsvInput] = useState('');
  const [showCsvImport, setShowCsvImport] = useState(false);

  const availableRoles: UserRole[] = ['berater', 'mentor', 'teamleiter', 'gebietsmanager', 'admin'];
  
  const handleUserSelection = useCallback((userId: string, checked: boolean) => {
    setSelectedUserIds(prev => 
      checked 
        ? [...prev, userId]
        : prev.filter(id => id !== userId)
    );
  }, []);

  const handleSelectAll = useCallback((checked: boolean) => {
    setSelectedUserIds(checked ? users.map(u => u.id) : []);
  }, [users]);

  const handleCsvImport = useCallback(() => {
    try {
      const lines = csvInput.trim().split('\n');
      const userEmails = lines.map(line => line.trim().toLowerCase()).filter(Boolean);
      
      const matchedUsers = users.filter(user => 
        userEmails.includes(user.email.toLowerCase())
      );
      
      setSelectedUserIds(matchedUsers.map(u => u.id));
      setCsvInput('');
      setShowCsvImport(false);
      
      toast.success(`${matchedUsers.length} Benutzer aus CSV importiert`);
    } catch (error) {
      toast.error('Fehler beim Importieren der CSV-Daten');
    }
  }, [csvInput, users]);

  const handleSubmit = async () => {
    if (!currentUser || !targetRole) return;

    const assignment: BulkRoleAssignment = {
      userIds: selectedUserIds,
      targetRole: targetRole as UserRole,
      removeExistingRoles,
      reason: reason.trim() || undefined,
      expiresAt: expiresAt || undefined
    };

    const validation = validateBulkRoleAssignment(currentUser, assignment);
    
    if (!validation.isValid) {
      validation.errors.forEach(error => toast.error(error));
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await onAssignRoles(assignment);
      
      clearInterval(progressInterval);
      setProgress(100);
      
      toast.success(`Rollen erfolgreich für ${selectedUserIds.length} Benutzer zugewiesen`);
      
      // Reset form
      setSelectedUserIds([]);
      setTargetRole('');
      setRemoveExistingRoles(false);
      setReason('');
      setExpiresAt('');
      
      setTimeout(() => {
        onClose();
        setProgress(0);
      }, 1000);
      
    } catch (error) {
      toast.error('Fehler bei der Rollenzuweisung');
      console.error('Bulk role assignment error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const selectedUsers = users.filter(user => selectedUserIds.includes(user.id));
  const canBulkAssign = currentUser && hasPermission(currentUser, 'user.bulk_assign');

  if (!canBulkAssign) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className={`${isMobile ? 'w-[95vw]' : 'max-w-2xl'}`}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Zugriff verweigert
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-gray-600">
              Sie haben keine Berechtigung für Massenzuweisungen von Rollen.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={onClose} variant="outline">
              Schließen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`${isMobile ? 'w-[95vw] h-[90vh]' : 'max-w-4xl max-h-[90vh]'} overflow-y-auto`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-600" />
            Massenzuweisung von Rollen
          </DialogTitle>
        </DialogHeader>

        {isProcessing && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3 mb-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Verarbeitung läuft...</span>
              </div>
              <Progress value={progress} className="w-full" />
              <p className="text-xs text-gray-600 mt-2">
                {progress < 100 ? 'Rollen werden zugewiesen...' : 'Abgeschlossen!'}
              </p>
            </CardContent>
          </Card>
        )}

        <div className="space-y-6">
          {/* User Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                Benutzer auswählen
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowCsvImport(!showCsvImport)}
                  >
                    <Upload className="h-4 w-4 mr-1" />
                    CSV Import
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSelectAll(selectedUserIds.length !== users.length)}
                  >
                    {selectedUserIds.length === users.length ? 'Alle abwählen' : 'Alle auswählen'}
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {showCsvImport && (
                <div className="mb-4 p-4 border rounded-lg bg-gray-50">
                  <Label htmlFor="csv-input" className="text-sm font-medium">
                    E-Mail-Adressen (eine pro Zeile)
                  </Label>
                  <Textarea
                    id="csv-input"
                    value={csvInput}
                    onChange={(e) => setCsvInput(e.target.value)}
                    placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                    className="mt-2"
                    rows={4}
                  />
                  <div className="flex gap-2 mt-2">
                    <Button onClick={handleCsvImport} className="min-h-[44px]">
                      Importieren
                    </Button>
                    <Button variant="outline" onClick={() => setShowCsvImport(false)} className="min-h-[44px]">
                      Abbrechen
                    </Button>
                  </div>
                </div>
              )}

              <div className="max-h-64 overflow-y-auto space-y-2">
                {users.map(user => (
                  <div key={user.id} className="flex items-center space-x-3 p-2 rounded hover:bg-gray-50">
                    <Checkbox
                      checked={selectedUserIds.includes(user.id)}
                      onCheckedChange={(checked) => handleUserSelection(user.id, checked as boolean)}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{user.name}</p>
                      <p className="text-xs text-gray-500 truncate">{user.email}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {formatSingleRole(user.role)}
                    </Badge>
                  </div>
                ))}
              </div>

              {selectedUsers.length > 0 && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-800">
                    {selectedUsers.length} Benutzer ausgewählt
                  </p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedUsers.slice(0, 5).map(user => (
                      <Badge key={user.id} variant="secondary" className="text-xs">
                        {user.name}
                      </Badge>
                    ))}
                    {selectedUsers.length > 5 && (
                      <Badge variant="secondary" className="text-xs">
                        +{selectedUsers.length - 5} weitere
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Role Assignment Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Rollenkonfiguration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="target-role">Zielrolle</Label>
                <Select value={targetRole} onValueChange={(value) => setTargetRole(value as UserRole)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Rolle auswählen" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map(role => (
                      <SelectItem key={role} value={role}>
                        {formatSingleRole(role)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remove-existing"
                  checked={removeExistingRoles}
                  onCheckedChange={setRemoveExistingRoles}
                />
                <Label htmlFor="remove-existing" className="text-sm">
                  Bestehende Rollen entfernen (nur neue Rolle behalten)
                </Label>
              </div>

              <div>
                <Label htmlFor="expiration">Ablaufdatum (optional)</Label>
                <Input
                  id="expiration"
                  type="datetime-local"
                  value={expiresAt}
                  onChange={(e) => setExpiresAt(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="reason">Grund für die Zuweisung (optional)</Label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Beschreiben Sie den Grund für diese Rollenzuweisung..."
                  className="mt-1"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            Abbrechen
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!targetRole || selectedUserIds.length === 0 || isProcessing}
            className="min-w-[120px]"
          >
            {isProcessing ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Verarbeitung...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Rollen zuweisen
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
