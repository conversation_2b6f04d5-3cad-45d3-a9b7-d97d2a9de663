
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useSettings } from '@/context/settings/SettingsProvider';
import { toast } from 'sonner';
import { Map, Settings, Eye, EyeOff } from 'lucide-react';

export const AppSettings: React.FC = () => {
  const { settings, updateSettings } = useSettings();
  const [showMapboxToken, setShowMapboxToken] = useState(false);

  const handleDefaultViewChange = (view: string) => {
    updateSettings({ defaultView: view });
    toast.success(`Standard-Ansicht auf ${view === 'list' ? 'Laufliste' : view === 'map' ? 'Kartenansicht' : 'Tagesübersicht'} geändert`);
  };

  const handleMapboxTokenChange = (token: string) => {
    updateSettings({ mapboxToken: token });
    if (token) {
      toast.success('Mapbox Token gespeichert');
    }
  };

  const handleZoomChange = (zoom: string) => {
    updateSettings({ defaultZoom: zoom });
    toast.success(`Standard-Zoom auf ${zoom} geändert`);
  };

  const handleAutoSaveChange = (autoSave: boolean) => {
    updateSettings({ autoSave });
    toast.success(`Automatisches Speichern ${autoSave ? 'aktiviert' : 'deaktiviert'}`);
  };

  const handleOfflineModeChange = (offlineMode: boolean) => {
    updateSettings({ offlineMode });
    toast.success(`Offline-Modus ${offlineMode ? 'aktiviert' : 'deaktiviert'}`);
  };

  const handleSave = () => {
    toast.success('App-Einstellungen gespeichert');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Ansicht-Einstellungen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Standard-Ansicht</Label>
            <Select value={settings.defaultView} onValueChange={handleDefaultViewChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="list">Laufliste</SelectItem>
                <SelectItem value="map">Kartenansicht</SelectItem>
                <SelectItem value="daily">Tagesübersicht</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Map className="h-5 w-5" />
            Karten-Einstellungen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="mapbox-token">Mapbox Token</Label>
            <div className="relative">
              <Input
                id="mapbox-token"
                type={showMapboxToken ? "text" : "password"}
                value={settings.mapboxToken}
                onChange={(e) => handleMapboxTokenChange(e.target.value)}
                placeholder="pk.ey..."
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowMapboxToken(!showMapboxToken)}
              >
                {showMapboxToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Persönlicher Mapbox Token für Kartendarstellung
            </p>
          </div>
          <div className="space-y-2">
            <Label>Standard-Zoom</Label>
            <Select value={settings.defaultZoom} onValueChange={handleZoomChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">Weit (10)</SelectItem>
                <SelectItem value="12">Normal (12)</SelectItem>
                <SelectItem value="14">Nah (14)</SelectItem>
                <SelectItem value="16">Sehr nah (16)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Allgemeine App-Einstellungen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Automatisches Speichern</Label>
              <p className="text-sm text-muted-foreground">
                Änderungen automatisch speichern
              </p>
            </div>
            <Switch
              checked={settings.autoSave}
              onCheckedChange={handleAutoSaveChange}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Offline-Modus</Label>
              <p className="text-sm text-muted-foreground">
                App auch ohne Internetverbindung nutzen
              </p>
            </div>
            <Switch
              checked={settings.offlineMode}
              onCheckedChange={handleOfflineModeChange}
            />
          </div>
          <Button onClick={handleSave} className="w-full md:w-auto">
            Einstellungen speichern
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
