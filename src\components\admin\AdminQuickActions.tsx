import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { User } from '@/types';
import { hasPermission } from '@/utils/roleUtils';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import {
  Download,
  Upload,
  RotateCcw,
  Database,
  Shield,
  Users,
  Settings,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface AdminQuickActionsProps {
  user: User;
  onRefresh: () => void;
}

export const AdminQuickActions: React.FC<AdminQuickActionsProps> = ({
  user,
  onRefresh
}) => {
  const isMobile = useIsMobile();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const handleDataExport = async () => {
    if (!hasPermission(user, 'data.export')) {
      toast.error('Keine Berechtigung für Datenexport');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(progressInterval);
      setExportProgress(100);

      // Create mock export data
      const exportData = {
        timestamp: new Date().toISOString(),
        users: [], // Would contain actual user data
        teams: [], // Would contain actual team data
        areas: [], // Would contain actual area data
        visits: [], // Would contain actual visit data
        metadata: {
          version: '1.0',
          exportedBy: user.id,
          totalRecords: 1000
        }
      };

      // Download the export
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `admin-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Datenexport erfolgreich abgeschlossen');
      
      setTimeout(() => {
        setExportProgress(0);
      }, 2000);

    } catch (error) {
      toast.error('Fehler beim Datenexport');
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleDataImport = async () => {
    if (!hasPermission(user, 'data.import')) {
      toast.error('Keine Berechtigung für Datenimport');
      return;
    }

    // Create file input
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      setIsImporting(true);
      
      try {
        const text = await file.text();
        const data = JSON.parse(text);
        
        // Validate import data structure
        if (!data.metadata || !data.timestamp) {
          throw new Error('Ungültiges Datenformat');
        }

        // Simulate import process
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        toast.success(`Datenimport erfolgreich: ${data.metadata.totalRecords || 0} Datensätze`);
        onRefresh();
        
      } catch (error) {
        toast.error('Fehler beim Datenimport: Ungültiges Dateiformat');
        console.error('Import error:', error);
      } finally {
        setIsImporting(false);
      }
    };
    
    input.click();
  };

  const handleSystemBackup = async () => {
    if (!hasPermission(user, 'data.backup')) {
      toast.error('Keine Berechtigung für System-Backup');
      return;
    }

    setIsBackingUp(true);
    
    try {
      // Simulate backup process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast.success('System-Backup erfolgreich erstellt');
      
    } catch (error) {
      toast.error('Fehler beim System-Backup');
      console.error('Backup error:', error);
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleClearCache = async () => {
    try {
      // Clear localStorage cache
      const keysToKeep = ['currentUser', 'sessionExpiry', 'rememberMe'];
      const allKeys = Object.keys(localStorage);
      
      allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
          localStorage.removeItem(key);
        }
      });

      // Clear sessionStorage
      sessionStorage.clear();
      
      toast.success('Cache erfolgreich geleert');
      onRefresh();
      
    } catch (error) {
      toast.error('Fehler beim Leeren des Cache');
      console.error('Cache clear error:', error);
    }
  };

  const quickActions = [
    {
      title: 'Daten exportieren',
      description: 'Vollständiger Export aller Systemdaten',
      icon: Download,
      action: handleDataExport,
      permission: 'data.export',
      loading: isExporting,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100'
    },
    {
      title: 'Daten importieren',
      description: 'Import von Daten aus JSON-Datei',
      icon: Upload,
      action: handleDataImport,
      permission: 'data.import',
      loading: isImporting,
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100'
    },
    {
      title: 'System-Backup',
      description: 'Vollständiges System-Backup erstellen',
      icon: Database,
      action: handleSystemBackup,
      permission: 'data.backup',
      loading: isBackingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100'
    },
    {
      title: 'Cache leeren',
      description: 'Browser-Cache und temporäre Daten löschen',
      icon: RotateCcw,
      action: handleClearCache,
      permission: 'system.config',
      loading: false,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 hover:bg-orange-100'
    }
  ];

  return (
    <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-blue-600" />
          Schnellaktionen
        </CardTitle>
        <CardDescription>
          Häufig verwendete Admin-Funktionen
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Export Progress */}
        {isExporting && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Export läuft...</span>
            </div>
            <Progress value={exportProgress} className="w-full" />
            <p className="text-xs text-blue-600 mt-1">
              {exportProgress < 100 ? 'Daten werden exportiert...' : 'Export abgeschlossen!'}
            </p>
          </div>
        )}

        {/* Quick Actions Grid */}
        <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-2'} gap-3`}>
          {quickActions.map((action, index) => {
            const canPerform = hasPermission(user, action.permission as any);
            
            return (
              <button
                key={index}
                onClick={action.action}
                disabled={!canPerform || action.loading}
                className={`
                  p-4 rounded-lg border transition-all duration-200 text-left touch-feedback
                  ${canPerform && !action.loading
                    ? `${action.bgColor} border-gray-200 hover:border-gray-300 hover:shadow-md`
                    : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-50'
                  }
                  min-h-[80px] min-w-[120px] flex flex-col justify-between
                `}
              >
                <div className="flex items-start justify-between mb-2">
                  <action.icon className={`h-6 w-6 ${canPerform ? action.color : 'text-gray-400'}`} />
                  {action.loading && (
                    <Clock className="h-5 w-5 text-gray-500 animate-spin" />
                  )}
                  {!canPerform && (
                    <Shield className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                
                <div>
                  <h3 className={`font-medium text-sm ${canPerform ? 'text-gray-900' : 'text-gray-500'}`}>
                    {action.title}
                  </h3>
                  <p className={`text-xs mt-1 ${canPerform ? 'text-gray-600' : 'text-gray-400'}`}>
                    {action.description}
                  </p>
                </div>
                
                {!canPerform && (
                  <Badge variant="secondary" className="text-xs mt-2 self-start">
                    Keine Berechtigung
                  </Badge>
                )}
              </button>
            );
          })}
        </div>

        {/* System Status Indicators */}
        <div className="pt-4 border-t">
          <h4 className="font-medium text-sm mb-3 text-gray-700">System-Status</h4>
          <div className="grid grid-cols-3 gap-3">
            <div className="text-center">
              <CheckCircle className="h-6 w-6 text-green-500 mx-auto mb-1" />
              <p className="text-xs text-gray-600">API</p>
              <p className="text-xs font-medium text-green-600">Online</p>
            </div>
            <div className="text-center">
              <CheckCircle className="h-6 w-6 text-green-500 mx-auto mb-1" />
              <p className="text-xs text-gray-600">Datenbank</p>
              <p className="text-xs font-medium text-green-600">Verbunden</p>
            </div>
            <div className="text-center">
              <AlertTriangle className="h-6 w-6 text-yellow-500 mx-auto mb-1" />
              <p className="text-xs text-gray-600">Cache</p>
              <p className="text-xs font-medium text-yellow-600">Voll</p>
            </div>
          </div>
        </div>

        {/* Recent Actions */}
        <div className="pt-4 border-t">
          <h4 className="font-medium text-sm mb-3 text-gray-700">Letzte Aktionen</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Datenexport</span>
              <span className="text-gray-500">vor 2 Stunden</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Cache geleert</span>
              <span className="text-gray-500">vor 1 Tag</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">System-Backup</span>
              <span className="text-gray-500">vor 3 Tagen</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
