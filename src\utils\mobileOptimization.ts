/**
 * Mobile-First Optimization Utilities
 * Provides utilities for ensuring mobile-first design compliance
 */

import { triggerHapticFeedback } from '@/hooks/useSwipeGestures';

// Touch target size constants (following Apple/Google guidelines)
export const TOUCH_TARGET_SIZES = {
  MINIMUM: 44, // Minimum recommended size
  COMFORTABLE: 48, // Comfortable size for most interactions
  LARGE: 56, // Large size for primary actions
  EXTRA_LARGE: 64, // Extra large for critical actions
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
} as const;

/**
 * Validates if an element meets touch target size requirements
 */
export const validateTouchTarget = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return rect.width >= TOUCH_TARGET_SIZES.MINIMUM && rect.height >= TOUCH_TARGET_SIZES.MINIMUM;
};

/**
 * Validates all touch targets on a page
 */
export const validateAllTouchTargets = (container?: HTMLElement): TouchTargetViolation[] => {
  const root = container || document.body;
  const interactiveElements = root.querySelectorAll(
    'button, input, select, textarea, [role="button"], [role="tab"], a, [tabindex]:not([tabindex="-1"]), .touch-target'
  );

  const violations: TouchTargetViolation[] = [];

  interactiveElements.forEach((element) => {
    const htmlElement = element as HTMLElement;

    // Skip non-interactive content containers
    if (isNonInteractiveContainer(htmlElement)) {
      return;
    }

    if (!validateTouchTarget(htmlElement)) {
      const rect = htmlElement.getBoundingClientRect();
      const violation: TouchTargetViolation = {
        element: htmlElement,
        currentSize: { width: rect.width, height: rect.height },
        tagName: htmlElement.tagName,
        className: htmlElement.className,
        id: htmlElement.id,
        textContent: htmlElement.textContent?.slice(0, 50) || '',
        selector: getElementSelector(htmlElement)
      };
      violations.push(violation);
      console.warn('Touch target too small:', violation);
    }
  });

  if (violations.length > 0) {
    console.warn(`Found ${violations.length} touch target violations`);
    console.table(violations.map(v => ({
      selector: v.selector,
      width: v.currentSize.width,
      height: v.currentSize.height,
      text: v.textContent
    })));
  }

  return violations;
};

interface TouchTargetViolation {
  element: HTMLElement;
  currentSize: { width: number; height: number };
  tagName: string;
  className: string;
  id: string;
  textContent: string;
  selector: string;
}

/**
 * Checks if an element is a non-interactive container that should be excluded from touch target validation
 */
const isNonInteractiveContainer = (element: HTMLElement): boolean => {
  // Skip Radix UI content containers that are not actually interactive
  const nonInteractiveSelectors = [
    '[data-radix-tabs-content]',
    '[data-radix-popover-content]',
    '[data-radix-dialog-content]',
    '[data-radix-select-content]',
    '[data-radix-tooltip-content]',
    '[data-radix-hover-card-content]',
    '[data-radix-menubar-content]',
    '[data-radix-dropdown-menu-content]',
    '[data-radix-context-menu-content]'
  ];

  // Check if element matches any non-interactive selector
  for (const selector of nonInteractiveSelectors) {
    if (element.matches(selector)) {
      return true;
    }
  }

  // Skip elements with 0x0 dimensions (likely hidden or not properly rendered)
  const rect = element.getBoundingClientRect();
  if (rect.width === 0 && rect.height === 0) {
    return true;
  }

  // Skip elements that are not visible
  if (element.offsetParent === null && element.style.position !== 'fixed') {
    return true;
  }

  // Skip Radix UI content containers by ID pattern
  if (element.id && element.id.includes('radix-') && element.id.includes('-content-')) {
    return true;
  }

  // Skip TabsContent specifically - these have focus-visible classes but aren't interactive
  if (element.tagName === 'DIV' &&
      element.className.includes('ring-offset-background') &&
      element.className.includes('focus-visible:outline-none') &&
      element.className.includes('focus-visible:ring-2')) {
    return true;
  }

  // Skip content containers based on class names and context
  const containerClasses = [
    'animate-fade-in'
  ];

  const hasContainerClass = containerClasses.some(cls =>
    element.className.includes(cls)
  );

  if (hasContainerClass && element.tagName === 'DIV' &&
      (element.className.includes('p-0') || element.className.includes('m-0'))) {
    return true;
  }

  return false;
};

/**
 * Gets a unique CSS selector for an element
 */
const getElementSelector = (element: HTMLElement): string => {
  if (element.id) return `#${element.id}`;

  let selector = element.tagName.toLowerCase();

  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.trim());
    if (classes.length > 0) {
      selector += '.' + classes.join('.');
    }
  }

  // Add parent context if needed
  const parent = element.parentElement;
  if (parent && parent !== document.body) {
    const parentSelector = parent.tagName.toLowerCase();
    if (parent.className) {
      const parentClasses = parent.className.split(' ').filter(c => c.trim()).slice(0, 2);
      if (parentClasses.length > 0) {
        return `${parentSelector}.${parentClasses.join('.')} > ${selector}`;
      }
    }
    return `${parentSelector} > ${selector}`;
  }

  return selector;
};

/**
 * Adds mobile-optimized event listeners with haptic feedback
 */
export const addMobileEventListeners = (element: HTMLElement, options: {
  onClick?: () => void;
  hapticType?: 'light' | 'medium' | 'heavy';
  preventDoubleClick?: boolean;
}): void => {
  const { onClick, hapticType = 'light', preventDoubleClick = true } = options;
  
  let lastClickTime = 0;

  const handleClick = (event: Event) => {
    if (preventDoubleClick) {
      const now = Date.now();
      if (now - lastClickTime < 300) {
        event.preventDefault();
        return;
      }
      lastClickTime = now;
    }

    triggerHapticFeedback(hapticType);
    onClick?.();
  };

  element.addEventListener('click', handleClick);
  element.addEventListener('touchstart', () => {
    element.style.transform = 'scale(0.96)';
  });
  element.addEventListener('touchend', () => {
    element.style.transform = '';
  });
};

/**
 * Checks if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
  return window.innerWidth < BREAKPOINTS.MOBILE;
};

/**
 * Checks if the current device is tablet
 */
export const isTabletDevice = (): boolean => {
  return window.innerWidth >= BREAKPOINTS.MOBILE && window.innerWidth < BREAKPOINTS.TABLET;
};

/**
 * Checks if the current device is desktop
 */
export const isDesktopDevice = (): boolean => {
  return window.innerWidth >= BREAKPOINTS.TABLET;
};

/**
 * Gets the appropriate touch target size for the current device
 */
export const getOptimalTouchTargetSize = (): number => {
  if (isMobileDevice()) return TOUCH_TARGET_SIZES.COMFORTABLE;
  if (isTabletDevice()) return TOUCH_TARGET_SIZES.MINIMUM;
  return 40; // Desktop standard
};

/**
 * Applies mobile-first optimizations to a container
 */
export const applyMobileOptimizations = (container: HTMLElement): void => {
  // Add mobile-specific classes
  if (isMobileDevice()) {
    container.classList.add('mobile-optimized');
  }

  // Validate touch targets
  validateAllTouchTargets(container);

  // Add touch feedback to interactive elements
  const interactiveElements = container.querySelectorAll('.touch-feedback');
  interactiveElements.forEach((element) => {
    addMobileEventListeners(element as HTMLElement, {
      hapticType: 'light'
    });
  });
};

/**
 * Optimizes form inputs for mobile
 */
export const optimizeFormForMobile = (form: HTMLFormElement): void => {
  const inputs = form.querySelectorAll('input, select, textarea');
  
  inputs.forEach((input) => {
    const element = input as HTMLInputElement;
    
    // Prevent zoom on iOS
    if (element.type === 'text' || element.type === 'email' || element.type === 'tel') {
      element.style.fontSize = '16px';
    }
    
    // Add mobile-specific attributes
    element.setAttribute('autocomplete', 'on');
    element.setAttribute('autocapitalize', 'words');
    
    // Ensure minimum touch target size
    if (!validateTouchTarget(element)) {
      element.style.minHeight = `${TOUCH_TARGET_SIZES.COMFORTABLE}px`;
    }
  });
};

/**
 * Debounced resize handler for responsive updates
 */
export const createResponsiveHandler = (callback: () => void, delay = 250) => {
  let timeoutId: NodeJS.Timeout;
  
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(callback, delay);
  };
};

/**
 * Sets up global mobile optimizations
 */
export const setupGlobalMobileOptimizations = (): void => {
  // Validate touch targets on page load
  document.addEventListener('DOMContentLoaded', () => {
    validateAllTouchTargets();
  });

  // Re-validate on dynamic content changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            validateAllTouchTargets(node as HTMLElement);
          }
        });
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Handle orientation changes
  window.addEventListener('orientationchange', createResponsiveHandler(() => {
    setTimeout(() => {
      validateAllTouchTargets();
    }, 500); // Wait for orientation change to complete
  }));

  // Handle resize events
  window.addEventListener('resize', createResponsiveHandler(() => {
    validateAllTouchTargets();
  }));
};
