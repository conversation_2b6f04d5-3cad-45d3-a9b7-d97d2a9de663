import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { BulkRoleAssignmentDialog } from '../BulkRoleAssignmentDialog';
import { User, BulkRoleAssignment } from '@/types';
import { AuthProvider } from '@/context/auth/AuthContext';

// Mock the auth context
const mockAuthContext = {
  user: {
    id: 'admin-1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin' as const,
    isActive: true
  },
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  isLoading: false,
  error: null,
  users: [],
  updateUser: vi.fn(),
  createUser: vi.fn(),
  loginWithGoogle: vi.fn(),
  clearError: vi.fn(),
  isAuthenticated: true,
  sessionExpiry: null,
  refreshSession: vi.fn()
};

vi.mock('@/context/auth', () => ({
  useAuth: () => mockAuthContext
}));

vi.mock('@/hooks/use-mobile', () => ({
  useIsMobile: () => false
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

const mockUsers: User[] = [
  {
    id: 'user-1',
    name: 'Test User 1',
    email: '<EMAIL>',
    role: 'berater',
    isActive: true
  },
  {
    id: 'user-2',
    name: 'Test User 2',
    email: '<EMAIL>',
    role: 'mentor',
    isActive: true
  },
  {
    id: 'user-3',
    name: 'Test User 3',
    email: '<EMAIL>',
    role: 'teamleiter',
    isActive: true
  }
];

describe('BulkRoleAssignmentDialog', () => {
  const mockOnClose = vi.fn();
  const mockOnAssignRoles = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = (props = {}) => {
    const defaultProps = {
      isOpen: true,
      onClose: mockOnClose,
      users: mockUsers,
      onAssignRoles: mockOnAssignRoles,
      ...props
    };

    return render(
      <AuthProvider>
        <BulkRoleAssignmentDialog {...defaultProps} />
      </AuthProvider>
    );
  };

  it('renders the dialog when open', () => {
    renderComponent();
    
    expect(screen.getByText('Massenzuweisung von Rollen')).toBeInTheDocument();
    expect(screen.getByText('Benutzer auswählen')).toBeInTheDocument();
    expect(screen.getByText('Rollenkonfiguration')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    renderComponent({ isOpen: false });
    
    expect(screen.queryByText('Massenzuweisung von Rollen')).not.toBeInTheDocument();
  });

  it('displays all users in the selection list', () => {
    renderComponent();
    
    mockUsers.forEach(user => {
      expect(screen.getByText(user.name)).toBeInTheDocument();
      expect(screen.getByText(user.email)).toBeInTheDocument();
    });
  });

  it('allows selecting and deselecting users', async () => {
    renderComponent();
    
    const firstUserCheckbox = screen.getAllByRole('checkbox')[0];
    
    // Select user
    fireEvent.click(firstUserCheckbox);
    expect(firstUserCheckbox).toBeChecked();
    
    // Deselect user
    fireEvent.click(firstUserCheckbox);
    expect(firstUserCheckbox).not.toBeChecked();
  });

  it('allows selecting all users', async () => {
    renderComponent();
    
    const selectAllButton = screen.getByText('Alle auswählen');
    fireEvent.click(selectAllButton);
    
    // Check that all user checkboxes are selected
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).toBeChecked();
    });
    
    expect(screen.getByText('3 Benutzer ausgewählt')).toBeInTheDocument();
  });

  it('allows deselecting all users', async () => {
    renderComponent();
    
    // First select all
    const selectAllButton = screen.getByText('Alle auswählen');
    fireEvent.click(selectAllButton);
    
    // Then deselect all
    const deselectAllButton = screen.getByText('Alle abwählen');
    fireEvent.click(deselectAllButton);
    
    // Check that no checkboxes are selected
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).not.toBeChecked();
    });
  });

  it('handles CSV import correctly', async () => {
    renderComponent();
    
    // Open CSV import
    const csvImportButton = screen.getByText('CSV Import');
    fireEvent.click(csvImportButton);
    
    // Enter CSV data
    const csvTextarea = screen.getByPlaceholderText(/<EMAIL>/);
    fireEvent.change(csvTextarea, {
      target: { value: '<EMAIL>\<EMAIL>' }
    });
    
    // Import
    const importButton = screen.getByText('Importieren');
    fireEvent.click(importButton);
    
    await waitFor(() => {
      expect(screen.getByText('2 Benutzer aus CSV importiert')).toBeInTheDocument();
    });
  });

  it('validates form before submission', async () => {
    renderComponent();
    
    const submitButton = screen.getByText('Rollen zuweisen');
    
    // Try to submit without selecting users or role
    fireEvent.click(submitButton);
    
    // Button should be disabled
    expect(submitButton).toBeDisabled();
  });

  it('submits form with valid data', async () => {
    mockOnAssignRoles.mockResolvedValue(undefined);
    renderComponent();
    
    // Select a user
    const firstUserCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstUserCheckbox);
    
    // Select a role
    const roleSelect = screen.getByRole('combobox');
    fireEvent.click(roleSelect);
    
    const adminOption = screen.getByText('Administrator');
    fireEvent.click(adminOption);
    
    // Add reason
    const reasonTextarea = screen.getByPlaceholderText(/Beschreiben Sie den Grund/);
    fireEvent.change(reasonTextarea, {
      target: { value: 'Test role assignment' }
    });
    
    // Submit
    const submitButton = screen.getByText('Rollen zuweisen');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnAssignRoles).toHaveBeenCalledWith({
        userIds: ['user-1'],
        targetRole: 'admin',
        removeExistingRoles: false,
        reason: 'Test role assignment',
        expiresAt: undefined
      });
    });
  });

  it('handles submission errors gracefully', async () => {
    const error = new Error('Assignment failed');
    mockOnAssignRoles.mockRejectedValue(error);
    renderComponent();
    
    // Select a user and role
    const firstUserCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstUserCheckbox);
    
    const roleSelect = screen.getByRole('combobox');
    fireEvent.click(roleSelect);
    
    const adminOption = screen.getByText('Administrator');
    fireEvent.click(adminOption);
    
    // Submit
    const submitButton = screen.getByText('Rollen zuweisen');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnAssignRoles).toHaveBeenCalled();
    });
  });

  it('shows access denied for users without permissions', () => {
    // Mock user without bulk assign permission
    const limitedUser = {
      ...mockAuthContext.user,
      role: 'berater' as const
    };
    
    vi.mocked(mockAuthContext).user = limitedUser;
    
    renderComponent();
    
    expect(screen.getByText('Zugriff verweigert')).toBeInTheDocument();
    expect(screen.getByText('Sie haben keine Berechtigung für Massenzuweisungen von Rollen.')).toBeInTheDocument();
  });

  it('handles expiration date correctly', async () => {
    mockOnAssignRoles.mockResolvedValue(undefined);
    renderComponent();
    
    // Select a user
    const firstUserCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstUserCheckbox);
    
    // Select a role
    const roleSelect = screen.getByRole('combobox');
    fireEvent.click(roleSelect);
    
    const adminOption = screen.getByText('Administrator');
    fireEvent.click(adminOption);
    
    // Set expiration date
    const expirationInput = screen.getByLabelText('Ablaufdatum (optional)');
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 30);
    const dateString = futureDate.toISOString().slice(0, 16);
    
    fireEvent.change(expirationInput, {
      target: { value: dateString }
    });
    
    // Submit
    const submitButton = screen.getByText('Rollen zuweisen');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnAssignRoles).toHaveBeenCalledWith(
        expect.objectContaining({
          expiresAt: dateString
        })
      );
    });
  });

  it('toggles remove existing roles option', () => {
    renderComponent();
    
    const removeExistingCheckbox = screen.getByLabelText(/Bestehende Rollen entfernen/);
    
    expect(removeExistingCheckbox).not.toBeChecked();
    
    fireEvent.click(removeExistingCheckbox);
    expect(removeExistingCheckbox).toBeChecked();
    
    fireEvent.click(removeExistingCheckbox);
    expect(removeExistingCheckbox).not.toBeChecked();
  });
});
