
import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import { MapPin, Plus, Edit, Trash2, Building } from 'lucide-react';
import { createArea, getAreas, updateArea, deleteArea } from '@/integrations/supabase/functions';
import { canAccessAdmin } from '@/utils/roleUtils';

interface Area {
  id: string;
  name: string;
  description?: string;
  postal_codes?: string[];
  created_at: string;
  updated_at: string;
}

const AreaManagementPage: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [areas, setAreas] = useState<Area[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedArea, setSelectedArea] = useState<Area | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    postal_codes: ''
  });

  useEffect(() => {
    loadAreas();
  }, []);

  const loadAreas = async () => {
    try {
      const data = await getAreas();
      setAreas(data || []);
    } catch (error) {
      console.error('Error loading areas:', error);
      toast.error('Fehler beim Laden der Gebiete');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateArea = async () => {
    if (!formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    try {
      const postalCodes = formData.postal_codes
        .split(',')
        .map(code => code.trim())
        .filter(code => code.length > 0);

      await createArea({
        name: formData.name,
        description: formData.description || undefined,
        postal_codes: postalCodes.length > 0 ? postalCodes : undefined
      });

      toast.success(`Gebiet "${formData.name}" wurde erstellt`);
      setFormData({ name: '', description: '', postal_codes: '' });
      setIsCreateDialogOpen(false);
      loadAreas();
    } catch (error) {
      console.error('Error creating area:', error);
      toast.error('Fehler beim Erstellen des Gebiets');
    }
  };

  const handleEditArea = async () => {
    if (!selectedArea || !formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    try {
      const postalCodes = formData.postal_codes
        .split(',')
        .map(code => code.trim())
        .filter(code => code.length > 0);

      await updateArea(selectedArea.id, {
        name: formData.name,
        description: formData.description || undefined,
        postal_codes: postalCodes.length > 0 ? postalCodes : undefined
      });

      toast.success(`Gebiet "${formData.name}" wurde aktualisiert`);
      setFormData({ name: '', description: '', postal_codes: '' });
      setSelectedArea(null);
      setIsEditDialogOpen(false);
      loadAreas();
    } catch (error) {
      console.error('Error updating area:', error);
      toast.error('Fehler beim Aktualisieren des Gebiets');
    }
  };

  const handleDeleteArea = async (area: Area) => {
    if (!confirm(`Möchten Sie das Gebiet "${area.name}" wirklich löschen?`)) {
      return;
    }

    try {
      await deleteArea(area.id);
      toast.success(`Gebiet "${area.name}" wurde gelöscht`);
      loadAreas();
    } catch (error) {
      console.error('Error deleting area:', error);
      toast.error('Fehler beim Löschen des Gebiets');
    }
  };

  const openEditDialog = (area: Area) => {
    setSelectedArea(area);
    setFormData({
      name: area.name,
      description: area.description || '',
      postal_codes: area.postal_codes?.join(', ') || ''
    });
    setIsEditDialogOpen(true);
  };

  if (!user || !canAccessAdmin(user)) {
    return (
      <MainLayout title="Gebiete verwalten">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
          <div className="text-center">
            <p className="text-gray-600">Sie haben keine Berechtigung, diese Seite anzuzeigen.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Gebiete verwalten">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
          {/* Header */}
          <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
            <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
              Gebiete verwalten
            </h1>
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
              Erstellen und verwalten Sie geografische Gebiete
            </p>
          </div>

          {/* Create Button */}
          <div className="flex justify-end">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                  <Plus className="h-4 w-4 mr-2" />
                  Neues Gebiet
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-blue-600" />
                    Neues Gebiet erstellen
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Name*</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="z.B. Stuttgart Nord"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Beschreibung</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Beschreibung des Gebiets..."
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="postal_codes">Postleitzahlen</Label>
                    <Input
                      id="postal_codes"
                      value={formData.postal_codes}
                      onChange={(e) => setFormData({ ...formData, postal_codes: e.target.value })}
                      placeholder="70173, 70174, 70176 (durch Komma getrennt)"
                    />
                  </div>
                  <Button onClick={handleCreateArea} className="w-full">
                    Gebiet erstellen
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Areas Table */}
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
            <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
              <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                <Building className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
                Alle Gebiete ({areas.length})
              </CardTitle>
            </CardHeader>
            <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
              {loading ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Lade Gebiete...</p>
                </div>
              ) : areas.length === 0 ? (
                <div className="text-center py-8">
                  <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Noch keine Gebiete erstellt</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Name</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Beschreibung</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>PLZ</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Aktionen</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {areas.map((area) => (
                        <TableRow key={area.id} className="hover:bg-blue-50/50 transition-colors">
                          <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-800`}>
                            {area.name}
                          </TableCell>
                          <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                            {area.description || '-'}
                          </TableCell>
                          <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                            {area.postal_codes?.length ? (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                {area.postal_codes.length} PLZ
                              </span>
                            ) : '-'}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openEditDialog(area)}
                                className="text-blue-600 border-blue-200 hover:bg-blue-50"
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteArea(area)}
                                className="text-red-600 border-red-200 hover:bg-red-50"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Edit Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Edit className="h-5 w-5 text-blue-600" />
                  Gebiet bearbeiten
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-name">Name*</Label>
                  <Input
                    id="edit-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="z.B. Stuttgart Nord"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-description">Beschreibung</Label>
                  <Textarea
                    id="edit-description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Beschreibung des Gebiets..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-postal-codes">Postleitzahlen</Label>
                  <Input
                    id="edit-postal-codes"
                    value={formData.postal_codes}
                    onChange={(e) => setFormData({ ...formData, postal_codes: e.target.value })}
                    placeholder="70173, 70174, 70176 (durch Komma getrennt)"
                  />
                </div>
                <Button onClick={handleEditArea} className="w-full">
                  Änderungen speichern
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </MainLayout>
  );
};

export default AreaManagementPage;
