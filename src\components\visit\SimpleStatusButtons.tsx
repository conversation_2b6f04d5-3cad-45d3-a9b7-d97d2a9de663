import React, { useState } from 'react';
import { VisitStatus } from '@/types';

interface SimpleStatusButtonsProps {
  currentStatus: VisitStatus;
  onStatusUpdate: (status: VisitStatus, comment?: string) => void;
  isUpdating: boolean;
}

export const SimpleStatusButtons: React.FC<SimpleStatusButtonsProps> = ({
  currentStatus,
  onStatusUpdate,
  isUpdating
}) => {
  const [selectedStatus, setSelectedStatus] = useState<VisitStatus | null>(null);
  const [comment, setComment] = useState('');
  const [showCommentField, setShowCommentField] = useState(false);

  const statusOptions = [
    {
      status: 'Angetroffen → Sale' as VisitStatus,
      label: '💰 Verkauf!',
      bgColor: 'bg-green-600 hover:bg-green-700',
    },
    {
      status: 'Angetroffen → Termin' as VisitStatus,
      label: '📅 Termin vereinbaren',
      bgColor: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      status: 'Angetroffen → Kein Interesse' as VisitStatus,
      label: '❌ Kein Interesse',
      bgColor: 'bg-neutral-500 hover:bg-neutral-600',
    },
    {
      status: 'N/A' as VisitStatus,
      label: '❓ Nicht angetroffen',
      bgColor: 'bg-red-600 hover:bg-red-700',
    },
  ];

  const handleStatusSelect = (status: VisitStatus) => {
    setSelectedStatus(status);
    setShowCommentField(true);
  };

  const handleSave = () => {
    if (selectedStatus) {
      onStatusUpdate(selectedStatus, comment.trim() || undefined);
      // Reset form
      setSelectedStatus(null);
      setComment('');
      setShowCommentField(false);
    }
  };

  const handleCancel = () => {
    setSelectedStatus(null);
    setComment('');
    setShowCommentField(false);
  };

  return (
    <div style={{ padding: '16px' }}>
      <h3 style={{
        fontSize: '20px',
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: '24px',
        color: '#1f2937'
      }}>
        Was ist passiert?
      </h3>

      {!showCommentField ? (
        // Status Selection
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {statusOptions.map((option) => (
            <button
              key={option.status}
              onClick={() => handleStatusSelect(option.status)}
              disabled={isUpdating || currentStatus === option.status}
              style={{
                width: '100%',
                height: '64px',
                borderRadius: '12px',
                fontSize: '18px',
                fontWeight: '600',
                color: 'white',
                border: 'none',
                cursor: isUpdating || currentStatus === option.status ? 'not-allowed' : 'pointer',
                opacity: isUpdating || currentStatus === option.status ? 0.5 : 1,
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              }}
              className={option.bgColor}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'scale(0.95)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
              }}
            >
              {option.label}
            </button>
          ))}
        </div>
      ) : (
        // Comment and Save Section
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* Selected Status Display */}
          <div style={{
            padding: '16px',
            borderRadius: '12px',
            backgroundColor: '#f3f4f6',
            border: '2px solid #e5e7eb',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '16px', fontWeight: '600', color: '#374151' }}>
              Gewählt: {statusOptions.find(opt => opt.status === selectedStatus)?.label}
            </div>
          </div>

          {/* Comment Field */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '8px'
            }}>
              Kommentar (optional)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="z.B. 'Niemand zu Hause', 'Hund gebellt', 'Termin für morgen'..."
              style={{
                width: '100%',
                minHeight: '80px',
                padding: '12px',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                fontSize: '16px',
                fontFamily: 'inherit',
                resize: 'vertical',
                outline: 'none',
                transition: 'border-color 0.2s ease'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#3b82f6';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e5e7eb';
              }}
              onKeyDown={(e) => {
                // Allow saving with Ctrl+Enter
                if (e.ctrlKey && e.key === 'Enter') {
                  handleSave();
                }
              }}
            />
            <div style={{
              fontSize: '12px',
              color: '#6b7280',
              marginTop: '4px'
            }}>
              💡 Tipp: Strg+Enter zum schnellen Speichern
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {/* Quick Save without comment */}
            <button
              onClick={() => {
                if (selectedStatus) {
                  onStatusUpdate(selectedStatus);
                  setSelectedStatus(null);
                  setComment('');
                  setShowCommentField(false);
                }
              }}
              disabled={isUpdating || !selectedStatus}
              style={{
                width: '100%',
                height: '48px',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: '600',
                color: 'white',
                backgroundColor: '#3b82f6',
                border: 'none',
                cursor: isUpdating || !selectedStatus ? 'not-allowed' : 'pointer',
                opacity: isUpdating || !selectedStatus ? 0.5 : 1,
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
              onMouseOver={(e) => {
                if (!isUpdating && selectedStatus) {
                  e.currentTarget.style.backgroundColor = '#2563eb';
                }
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#3b82f6';
              }}
            >
              {isUpdating ? '⏳ Speichern...' : '⚡ Schnell speichern (ohne Kommentar)'}
            </button>

            {/* Save with comment and Cancel */}
            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={handleCancel}
                disabled={isUpdating}
                style={{
                  flex: '1',
                  height: '48px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#6b7280',
                  backgroundColor: '#f9fafb',
                  border: '2px solid #e5e7eb',
                  cursor: isUpdating ? 'not-allowed' : 'pointer',
                  opacity: isUpdating ? 0.5 : 1,
                  transition: 'all 0.2s ease'
                }}
                onMouseOver={(e) => {
                  if (!isUpdating) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                  e.currentTarget.style.borderColor = '#e5e7eb';
                }}
              >
                Abbrechen
              </button>

              <button
                onClick={handleSave}
                disabled={isUpdating || !selectedStatus}
                style={{
                  flex: '2',
                  height: '48px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: 'white',
                  backgroundColor: '#10b981',
                  border: 'none',
                  cursor: isUpdating || !selectedStatus ? 'not-allowed' : 'pointer',
                  opacity: isUpdating || !selectedStatus ? 0.5 : 1,
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseOver={(e) => {
                  if (!isUpdating && selectedStatus) {
                    e.currentTarget.style.backgroundColor = '#059669';
                  }
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#10b981';
                }}
              >
                {isUpdating ? '⏳ Speichern...' : '💾 Mit Kommentar speichern'}
              </button>
            </div>
          </div>
        </div>
      )}

      {currentStatus !== 'N/A' && (
        <div style={{
          marginTop: '16px',
          textAlign: 'center',
          fontSize: '14px',
          color: '#059669',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px'
        }}>
          <span>✅</span>
          <span>Aktueller Status: {currentStatus}</span>
        </div>
      )}
    </div>
  );
};
