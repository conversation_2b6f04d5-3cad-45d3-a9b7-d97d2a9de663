import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  MapPin,
  Navigation,
  Clock,
  Zap,
  Target,
  CheckCircle,
  TrendingUp,
  Calendar,
  Users,
  Home,
  Building,
  Search,
  Filter,
  BarChart3
} from 'lucide-react';
import { useData } from '@/context/data';
import { triggerHapticFeedback, validateTouchTarget } from '@/hooks/useSwipeGestures';
import { toast } from 'sonner';
import { format, isToday, isYesterday, startOfWeek, endOfWeek } from 'date-fns';
import { de } from 'date-fns/locale';

interface EnhancedVisitTrackerProps {
  className?: string;
  onVisitComplete?: (visitId: string) => void;
}

export const EnhancedVisitTracker: React.FC<EnhancedVisitTrackerProps> = ({
  className,
  onVisitComplete
}) => {
  const { visits, houses, addresses, doors, products } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterTimeframe, setFilterTimeframe] = useState<string>('today');
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate statistics
  const getVisitStats = () => {
    const today = new Date();
    const todayVisits = visits.filter(visit => isToday(new Date(visit.timestamp)));
    const yesterdayVisits = visits.filter(visit => isYesterday(new Date(visit.timestamp)));
    const weekStart = startOfWeek(today, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
    const weekVisits = visits.filter(visit => {
      const visitDate = new Date(visit.timestamp);
      return visitDate >= weekStart && visitDate <= weekEnd;
    });

    const successfulToday = todayVisits.filter(v => v.status === 'Angetroffen → Sale').length;
    const appointmentsToday = todayVisits.filter(v => v.status === 'Angetroffen → Termin').length;
    const notMetToday = todayVisits.filter(v => v.status === 'N/A').length;

    return {
      todayTotal: todayVisits.length,
      yesterdayTotal: yesterdayVisits.length,
      weekTotal: weekVisits.length,
      successfulToday,
      appointmentsToday,
      notMetToday,
      successRate: todayVisits.length > 0 ? (successfulToday / todayVisits.length) * 100 : 0
    };
  };

  const stats = getVisitStats();

  // Filter visits based on search and filters
  const getFilteredVisits = () => {
    let filteredVisits = visits;

    // Time filter
    if (filterTimeframe === 'today') {
      filteredVisits = filteredVisits.filter(visit => isToday(new Date(visit.timestamp)));
    } else if (filterTimeframe === 'yesterday') {
      filteredVisits = filteredVisits.filter(visit => isYesterday(new Date(visit.timestamp)));
    } else if (filterTimeframe === 'week') {
      const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
      const weekEnd = endOfWeek(new Date(), { weekStartsOn: 1 });
      filteredVisits = filteredVisits.filter(visit => {
        const visitDate = new Date(visit.timestamp);
        return visitDate >= weekStart && visitDate <= weekEnd;
      });
    }

    // Status filter
    if (filterStatus !== 'all') {
      filteredVisits = filteredVisits.filter(visit => visit.status === filterStatus);
    }

    // Search filter
    if (searchTerm) {
      filteredVisits = filteredVisits.filter(visit => {
        const house = houses.find(h => h.id === visit.houseId);
        const address = house ? addresses.find(a => a.id === house.addressId) : null;
        const searchString = `${address?.street || ''} ${house?.houseNumber || ''} ${address?.city || ''}`.toLowerCase();
        return searchString.includes(searchTerm.toLowerCase());
      });
    }

    return filteredVisits.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  // Get visit details
  const getVisitDetails = (visitId: string) => {
    const visit = visits.find(v => v.id === visitId);
    if (!visit) return null;

    const house = houses.find(h => h.id === visit.houseId);
    const address = house ? addresses.find(a => a.id === house.addressId) : null;
    const visitDoors = doors.filter(d => d.visitId === visitId);
    const visitProducts = visitDoors.flatMap(door =>
      products.filter(p => p.doorId === door.id)
    );

    return {
      visit,
      house,
      address,
      doors: visitDoors,
      products: visitProducts
    };
  };

  // Validate touch targets on mount
  useEffect(() => {
    if (containerRef.current) {
      const buttons = containerRef.current.querySelectorAll('button');
      buttons.forEach(button => {
        if (!validateTouchTarget(button as HTMLElement)) {
          console.warn('Touch target too small:', button);
        }
      });
    }
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Angetroffen → Sale':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Angetroffen → Termin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Angetroffen → Kein Interesse':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'N/A':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Angetroffen → Sale':
        return <CheckCircle className="h-4 w-4" />;
      case 'Angetroffen → Termin':
        return <Calendar className="h-4 w-4" />;
      case 'Angetroffen → Kein Interesse':
        return <Users className="h-4 w-4" />;
      case 'N/A':
        return <Clock className="h-4 w-4" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };

  const filteredVisits = getFilteredVisits();

  return (
    <div className={className} ref={containerRef}>
      {/* Statistics Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Heute</span>
            </div>
            <div className="text-2xl font-bold text-blue-900">{stats.todayTotal}</div>
            <div className="text-xs text-blue-700">
              {stats.yesterdayTotal > 0 && (
                <span className={stats.todayTotal >= stats.yesterdayTotal ? 'text-green-600' : 'text-red-600'}>
                  {stats.todayTotal >= stats.yesterdayTotal ? '↗' : '↘'}
                  {Math.abs(stats.todayTotal - stats.yesterdayTotal)} vs. gestern
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">Sales</span>
            </div>
            <div className="text-2xl font-bold text-green-900">{stats.successfulToday}</div>
            <div className="text-xs text-green-700">
              {stats.successRate.toFixed(1)}% Erfolgsrate
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">Termine</span>
            </div>
            <div className="text-2xl font-bold text-orange-900">{stats.appointmentsToday}</div>
            <div className="text-xs text-orange-700">Vereinbart</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-5 w-5 text-red-600" />
              <span className="text-sm font-medium text-red-800">Nicht angetroffen</span>
            </div>
            <div className="text-2xl font-bold text-red-900">{stats.notMetToday}</div>
            <div className="text-xs text-red-700">Versuche</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-4">
          <div className="space-y-4">
            {/* Search */}
            <div>
              <Label htmlFor="search" className="text-sm font-medium mb-2 block">
                Suche nach Adresse
              </Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  type="text"
                  placeholder="Straße, Hausnummer oder Stadt..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 text-base"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="timeframe" className="text-sm font-medium mb-2 block">
                  Zeitraum
                </Label>
                <select
                  id="timeframe"
                  value={filterTimeframe}
                  onChange={(e) => setFilterTimeframe(e.target.value)}
                  className="w-full h-12 px-3 border border-gray-200 rounded-lg text-base bg-white"
                >
                  <option value="today">Heute</option>
                  <option value="yesterday">Gestern</option>
                  <option value="week">Diese Woche</option>
                  <option value="all">Alle</option>
                </select>
              </div>

              <div>
                <Label htmlFor="status" className="text-sm font-medium mb-2 block">
                  Status
                </Label>
                <select
                  id="status"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="w-full h-12 px-3 border border-gray-200 rounded-lg text-base bg-white"
                >
                  <option value="all">Alle Status</option>
                  <option value="Angetroffen → Sale">Sales</option>
                  <option value="Angetroffen → Termin">Termine</option>
                  <option value="Angetroffen → Kein Interesse">Kein Interesse</option>
                  <option value="N/A">Nicht angetroffen</option>
                </select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Visit List */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BarChart3 className="h-5 w-5 text-purple-500" />
            Besuche ({filteredVisits.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredVisits.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">Keine Besuche gefunden</p>
              <p className="text-sm">
                {searchTerm || filterStatus !== 'all' || filterTimeframe !== 'today'
                  ? 'Versuchen Sie andere Filter oder Suchbegriffe'
                  : 'Starten Sie Ihren ersten Besuch!'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredVisits.map((visit) => {
                const details = getVisitDetails(visit.id);
                if (!details) return null;

                const { house, address, doors: visitDoors, products: visitProducts } = details;

                return (
                  <div key={visit.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          {house?.type === 'EFH' ? (
                            <Home className="h-4 w-4 text-blue-500" />
                          ) : (
                            <Building className="h-4 w-4 text-purple-500" />
                          )}
                          <span className="font-medium text-lg">
                            {address?.street} {house?.houseNumber}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">
                          {address?.zipCode} {address?.city}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {format(new Date(visit.timestamp), 'dd.MM.yyyy HH:mm', { locale: de })}
                        </p>
                      </div>

                      <Badge className={`${getStatusColor(visit.status)} border`}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(visit.status)}
                          <span className="text-xs font-medium">
                            {visit.status === 'N/A' ? 'Nicht angetroffen' :
                             visit.status === 'Angetroffen → Sale' ? 'Sale' :
                             visit.status === 'Angetroffen → Termin' ? 'Termin' :
                             'Kein Interesse'}
                          </span>
                        </div>
                      </Badge>
                    </div>

                    {/* Doors and Products */}
                    {visitDoors.length > 0 && (
                      <div className="space-y-2">
                        {visitDoors.map((door) => {
                          const doorProducts = visitProducts.filter(p => p.doorId === door.id);
                          return (
                            <div key={door.id} className="bg-gray-50 rounded p-3">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium text-sm">{door.name}</span>
                                <Badge className={`${getStatusColor(door.status)} border text-xs`}>
                                  {door.status === 'N/A' ? 'Nicht angetroffen' :
                                   door.status === 'Angetroffen → Sale' ? 'Sale' :
                                   door.status === 'Angetroffen → Termin' ? 'Termin' :
                                   'Kein Interesse'}
                                </Badge>
                              </div>

                              {doorProducts.length > 0 && (
                                <div className="space-y-1">
                                  <p className="text-xs font-medium text-gray-700">Produkte:</p>
                                  {doorProducts.map((product) => (
                                    <div key={product.id} className="text-xs text-gray-600">
                                      {product.quantity}x {product.type} ({product.category})
                                    </div>
                                  ))}
                                </div>
                              )}

                              {door.comment && (
                                <div className="mt-2 text-xs text-gray-600">
                                  <span className="font-medium">Kommentar:</span> {door.comment}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}

                    {visit.comment && (
                      <div className="mt-3 p-2 bg-blue-50 rounded text-sm">
                        <span className="font-medium text-blue-800">Notiz:</span>
                        <span className="text-blue-700 ml-1">{visit.comment}</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

    </div>
  );
};

export default EnhancedVisitTracker;
