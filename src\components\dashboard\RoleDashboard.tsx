
import React, { useState } from 'react';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';
import { UserRole } from '@/types';
import { isMultiRoleUser, getAvailableRoles, formatSingleRole, hasRole } from '@/utils/roleUtils';
import BeraterDashboard from './BeraterDashboard';
import MentorDashboard from './MentorDashboard';
import TeamleiterDashboard from './TeamleiterDashboard';
import GebietsmanagerDashboard from './GebietsmanagerDashboard';
import AdminDashboard from './AdminDashboard';

const RoleDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeRole, setActiveRole] = useState<UserRole | null>(null);

  if (!user) {
    return (
      <Card className="h-full flex items-center justify-center">
        <CardContent className="pt-6 text-center">
          <p className="text-sm md:text-base">Bitte melden Sie sich an, um Ihre Übersicht zu sehen.</p>
          <Button
            onClick={() => navigate('/login')}
            className="mt-4 text-sm md:text-base"
          >
            Zum Login
          </Button>
        </CardContent>
      </Card>
    );
  }

  // For multi-role users, show role selector
  const availableRoles = getAvailableRoles(user);
  const currentRole = activeRole || user.role;
  const showRoleSelector = isMultiRoleUser(user);

  const renderDashboard = (role: UserRole) => {
    switch (role) {
      case 'berater':
        return <BeraterDashboard userId={user.id} />;
      case 'mentor':
        return <MentorDashboard mentorId={user.id} />;
      case 'teamleiter':
        return <TeamleiterDashboard teamId={user.teamId || ''} />;
      case 'gebietsmanager':
        return <GebietsmanagerDashboard />;
      case 'admin':
        return <AdminDashboard />;
      default:
        return (
          <Card className="h-full flex items-center justify-center">
            <CardHeader>
              <CardTitle className="text-base md:text-lg">Willkommen</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm md:text-base">Für Ihre Rolle ist keine spezifische Übersicht verfügbar.</p>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="space-y-4">
      {/* Role Selector for Multi-Role Users */}
      {showRoleSelector && (
        <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Aktive Rolle wählen:
                </h3>
                <Select value={currentRole} onValueChange={(value) => setActiveRole(value as UserRole)}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Rolle auswählen" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map((role) => (
                      <SelectItem key={role} value={role}>
                        {formatSingleRole(role)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="text-xs text-gray-500">
                <p>Verfügbare Rollen:</p>
                <p className="font-medium">{availableRoles.map(formatSingleRole).join(', ')}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Content */}
      {renderDashboard(currentRole)}
    </div>
  );
};

export default RoleDashboard;
