import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/auth';
import { useData } from '@/context/data';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import {
  Users,
  Shield,
  Settings,
  Activity,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Download,
  Upload,
  RotateCcw,
  Zap,
  MapPin,
  Building
} from 'lucide-react';
import { BulkRoleAssignmentDialog } from './BulkRoleAssignmentDialog';
import { SystemHealthMonitor } from './SystemHealthMonitor';
import { FeatureToggleManager } from './FeatureToggleManager';
import { AdminQuickActions } from './AdminQuickActions';
import { hasPermission, canPerformAdminAction } from '@/utils/roleUtils';
import { BulkRoleAssignment, SystemHealth, FeatureToggle } from '@/types';
import { getAreas, getTeams, getAuditLogs, getUserProfiles, logAdminAction } from '@/integrations/supabase/functions';

export const EnhancedAdminDashboard: React.FC = () => {
  const { user, users, updateUser } = useAuth();
  const { addresses, houses, visits, doors, products } = useData();
  const isMobile = useIsMobile();
  
  const [areas, setAreas] = useState<any[]>([]);
  const [teams, setTeams] = useState<any[]>([]);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [userProfiles, setUserProfiles] = useState<any[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [featureToggles, setFeatureToggles] = useState<FeatureToggle[]>([]);
  const [loading, setLoading] = useState(true);
  const [isBulkRoleDialogOpen, setIsBulkRoleDialogOpen] = useState(false);

  useEffect(() => {
    loadAdminData();
    loadSystemHealth();
    loadFeatureToggles();
  }, []);

  const loadAdminData = async () => {
    try {
      const [areasData, teamsData, auditData, profilesData] = await Promise.all([
        getAreas(),
        getTeams(),
        getAuditLogs(50),
        getUserProfiles()
      ]);
      
      setAreas(areasData || []);
      setTeams(teamsData || []);
      setAuditLogs(auditData || []);
      setUserProfiles(profilesData || []);
    } catch (error) {
      console.error('Error loading admin data:', error);
      toast.error('Fehler beim Laden der Admin-Daten');
    } finally {
      setLoading(false);
    }
  };

  const loadSystemHealth = async () => {
    // Mock system health data - in production, this would come from monitoring APIs
    const mockHealth: SystemHealth = {
      status: 'healthy',
      uptime: 99.9,
      memoryUsage: 65,
      activeUsers: users.filter(u => u.isActive !== false).length,
      errorRate: 0.1,
      responseTime: 180,
      lastChecked: new Date().toISOString()
    };
    setSystemHealth(mockHealth);
  };

  const loadFeatureToggles = async () => {
    // Mock feature toggles - in production, this would come from a feature flag service
    const mockToggles: FeatureToggle[] = [
      {
        id: '1',
        name: 'advanced_analytics',
        description: 'Erweiterte Analytik-Features',
        enabled: true,
        rolloutPercentage: 100,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'mobile_offline_sync',
        description: 'Mobile Offline-Synchronisation',
        enabled: false,
        rolloutPercentage: 0,
        targetRoles: ['admin', 'teamleiter'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    setFeatureToggles(mockToggles);
  };

  const handleBulkRoleAssignment = async (assignment: BulkRoleAssignment) => {
    if (!user) return;

    try {
      // Log the admin action
      await logAdminAction({
        admin_user_id: user.id,
        action_type: 'role_assignment',
        target_type: 'user',
        description: `Bulk role assignment: ${assignment.targetRole} to ${assignment.userIds.length} users`,
        new_data: assignment
      });

      // Update users with new roles
      for (const userId of assignment.userIds) {
        const targetUser = users.find(u => u.id === userId);
        if (targetUser) {
          const updatedUser = {
            ...targetUser,
            role: assignment.targetRole,
            roles: assignment.removeExistingRoles ? [assignment.targetRole] : [...(targetUser.roles || [targetUser.role]), assignment.targetRole],
            isMultiRole: !assignment.removeExistingRoles && targetUser.roles && targetUser.roles.length > 0,
            updatedAt: new Date().toISOString()
          };
          
          await updateUser(updatedUser);
        }
      }

      // Reload admin data
      await loadAdminData();
      
    } catch (error) {
      console.error('Error in bulk role assignment:', error);
      throw error;
    }
  };

  const quickStats = [
    {
      title: "Aktive Benutzer",
      value: userProfiles.filter(p => p.is_active !== false).length,
      total: userProfiles.length,
      icon: Users,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600",
      change: "+5%"
    },
    {
      title: "Teams",
      value: teams.length,
      icon: Building,
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600"
    },
    {
      title: "Gebiete",
      value: areas.length,
      icon: MapPin,
      color: "from-green-500 to-green-600",
      textColor: "text-green-600"
    },
    {
      title: "System Status",
      value: systemHealth?.status === 'healthy' ? 'Gesund' : 'Warnung',
      icon: systemHealth?.status === 'healthy' ? CheckCircle : AlertTriangle,
      color: systemHealth?.status === 'healthy' ? "from-green-500 to-green-600" : "from-yellow-500 to-yellow-600",
      textColor: systemHealth?.status === 'healthy' ? "text-green-600" : "text-yellow-600"
    }
  ];

  if (!user || !hasPermission(user, 'system.config')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Zugriff verweigert</h3>
          <p className="text-gray-600">Sie haben keine Berechtigung für das Admin-Dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Zentrale Verwaltung und Systemüberwachung</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setIsBulkRoleDialogOpen(true)}
            disabled={!hasPermission(user, 'user.bulk_assign')}
            className="min-h-[44px]"
          >
            <Users className="h-4 w-4 mr-2" />
            Massenzuweisung
          </Button>
          <Button variant="outline" onClick={loadAdminData} className="min-h-[44px]">
            <RotateCcw className="h-4 w-4 mr-2" />
            Aktualisieren
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickStats.map((stat, index) => (
          <Card key={index} className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    {stat.change && (
                      <Badge variant="secondary" className="text-xs">
                        {stat.change}
                      </Badge>
                    )}
                  </div>
                  {stat.total && (
                    <p className="text-xs text-gray-500">von {stat.total} gesamt</p>
                  )}
                </div>
                <div className={`p-3 rounded-full bg-gradient-to-r ${stat.color}`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
          <TabsTrigger value="overview">Übersicht</TabsTrigger>
          <TabsTrigger value="users">Benutzer</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="audit">Audit</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AdminQuickActions user={user} onRefresh={loadAdminData} />
            <SystemHealthMonitor health={systemHealth} />
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Benutzerverwaltung
              </CardTitle>
              <CardDescription>
                Verwalten Sie Benutzerrollen und -berechtigungen
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-gray-600">
                  {userProfiles.length} Benutzer registriert
                </p>
                <Button
                  onClick={() => setIsBulkRoleDialogOpen(true)}
                  disabled={!hasPermission(user, 'user.bulk_assign')}
                  size="sm"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Massenzuweisung
                </Button>
              </div>
              
              <div className="space-y-2">
                {userProfiles.slice(0, 5).map((profile) => (
                  <div key={profile.id} className="flex items-center justify-between p-3 rounded-lg border">
                    <div>
                      <p className="font-medium">{profile.full_name || profile.email}</p>
                      <p className="text-sm text-gray-500">{profile.email}</p>
                    </div>
                    <Badge variant={profile.is_active ? "default" : "secondary"}>
                      {profile.is_active ? "Aktiv" : "Inaktiv"}
                    </Badge>
                  </div>
                ))}
              </div>
              
              {userProfiles.length > 5 && (
                <p className="text-sm text-gray-500 mt-4 text-center">
                  und {userProfiles.length - 5} weitere...
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <SystemHealthMonitor health={systemHealth} detailed />
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <FeatureToggleManager 
            toggles={featureToggles} 
            onUpdate={setFeatureToggles}
            canManage={hasPermission(user, 'system.config')}
          />
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-orange-600" />
                Audit-Protokoll
              </CardTitle>
              <CardDescription>
                Letzte {auditLogs.length} Admin-Aktionen im System
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
                  <p className="text-gray-500">Lade Audit-Logs...</p>
                </div>
              ) : auditLogs.length === 0 ? (
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Noch keine Admin-Aktionen protokolliert</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {auditLogs.slice(0, 10).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50">
                      <div>
                        <p className="font-medium text-sm">{log.action_type}</p>
                        <p className="text-xs text-gray-500">{log.description}</p>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline" className="text-xs">
                          {log.target_type}
                        </Badge>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(log.created_at).toLocaleDateString('de-DE')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Bulk Role Assignment Dialog */}
      <BulkRoleAssignmentDialog
        isOpen={isBulkRoleDialogOpen}
        onClose={() => setIsBulkRoleDialogOpen(false)}
        users={users}
        onAssignRoles={handleBulkRoleAssignment}
      />
    </div>
  );
};
