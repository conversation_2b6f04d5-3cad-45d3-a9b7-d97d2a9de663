import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { validateAllTouchTargets } from '@/utils/mobileOptimization';
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface TouchTargetViolation {
  element: HTMLElement;
  currentSize: { width: number; height: number };
  tagName: string;
  className: string;
  id: string;
  textContent: string;
  selector: string;
}

export const TouchTargetValidationTest: React.FC = () => {
  const [violations, setViolations] = useState<TouchTargetViolation[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] = useState<Date | null>(null);

  const runValidation = () => {
    setIsValidating(true);
    
    // Small delay to show loading state
    setTimeout(() => {
      const foundViolations = validateAllTouchTargets();
      setViolations(foundViolations);
      setLastValidation(new Date());
      setIsValidating(false);
    }, 500);
  };

  useEffect(() => {
    // Run initial validation
    runValidation();
  }, []);

  const getStatusIcon = () => {
    if (violations.length === 0) {
      return <CheckCircle className="h-6 w-6 text-green-600" />;
    }
    return <XCircle className="h-6 w-6 text-red-600" />;
  };

  const getStatusText = () => {
    if (violations.length === 0) {
      return 'All touch targets meet 44px minimum requirement';
    }
    return `Found ${violations.length} touch target violations`;
  };

  return (
    <div className="space-y-6 p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            {getStatusIcon()}
            Touch Target Validation Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status Summary */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="font-medium">{getStatusText()}</p>
              {lastValidation && (
                <p className="text-sm text-gray-500 mt-1">
                  Last validated: {lastValidation.toLocaleTimeString()}
                </p>
              )}
            </div>

            {/* Validation Button */}
            <Button 
              onClick={runValidation} 
              disabled={isValidating}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isValidating ? 'animate-spin' : ''}`} />
              {isValidating ? 'Validating...' : 'Run Validation'}
            </Button>

            {/* Test Components */}
            <div className="mt-6">
              <h3 className="font-semibold mb-4">Test Components:</h3>
              
              {/* Tabs Component Test */}
              <Tabs defaultValue="efh" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="efh">EFH</TabsTrigger>
                  <TabsTrigger value="mfh">MFH</TabsTrigger>
                </TabsList>
                <TabsContent value="efh" className="p-4 border rounded">
                  <p>EFH Content - This should not trigger touch target violations</p>
                  <Button className="mt-2">EFH Button</Button>
                </TabsContent>
                <TabsContent value="mfh" className="p-4 border rounded">
                  <p>MFH Content - This should not trigger touch target violations</p>
                  <Button className="mt-2">MFH Button</Button>
                </TabsContent>
              </Tabs>
            </div>

            {/* Violations Details */}
            {violations.length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold mb-4 text-red-600">Violations Found:</h3>
                <div className="space-y-3">
                  {violations.map((violation, index) => (
                    <div key={index} className="p-3 border border-red-200 rounded bg-red-50">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{violation.tagName}</p>
                          <p className="text-sm text-gray-600">
                            Size: {violation.currentSize.width.toFixed(0)}×{violation.currentSize.height.toFixed(0)}px
                          </p>
                          {violation.id && (
                            <p className="text-xs text-gray-500">ID: {violation.id}</p>
                          )}
                          {violation.textContent && (
                            <p className="text-xs text-gray-500">Text: "{violation.textContent}"</p>
                          )}
                        </div>
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {violation.selector}
                        </code>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Success Message */}
            {violations.length === 0 && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <p className="text-green-800 font-medium">
                    ✅ All touch targets are properly sized for mobile accessibility!
                  </p>
                </div>
                <p className="text-green-700 text-sm mt-1">
                  The validation now correctly excludes non-interactive content containers.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TouchTargetValidationTest;
