import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { EnhancedAdminDashboard } from '@/components/admin/EnhancedAdminDashboard';
import { useAuth } from '@/context/auth';
import { canAccessAdmin } from '@/utils/roleUtils';
import { AlertTriangle } from 'lucide-react';

const AdminDashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (!user || !canAccessAdmin(user)) {
    return (
      <MainLayout title="Admin Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Zugriff verweigert</h3>
            <p className="text-gray-600"><PERSON>e haben keine Berechtigung für das Admin-Dashboard.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Admin Dashboard">
      <div className="container mx-auto px-4 py-6">
        <EnhancedAdminDashboard />
      </div>
    </MainLayout>
  );
};

export default AdminDashboardPage;
