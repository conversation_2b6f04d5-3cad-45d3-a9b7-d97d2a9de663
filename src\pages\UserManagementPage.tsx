import React, { useState, useEffect, useMemo } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, UserRole } from '@/types';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users } from 'lucide-react';
import { canAccessAdmin } from '@/utils/roleUtils';
import { getUserProfiles, updateUserProfile, resetUserStatistics, getTeams } from '@/integrations/supabase/functions';
import { UserTable } from '@/components/user-management/UserTable';
import { UserViewDialog } from '@/components/user-management/UserViewDialog';
import { UserEditDialog } from '@/components/user-management/UserEditDialog';
import { CreateUserDialog } from '@/components/user-management/CreateUserDialog';
import { UserFilters } from '@/components/user-management/UserFilters';
import { UserResetDialog } from '@/components/user-management/UserResetDialog';

interface ExtendedUser extends User {
  is_active?: boolean;
  total_visits?: number;
  total_sales?: number;
  stats_reset_count?: number;
  last_stats_reset?: string;
}

interface Team {
  id: string;
  name: string;
}

const UserManagementPage: React.FC = () => {
  const { users, updateUser, createUser, user: currentUser } = useAuth();
  const isMobile = useIsMobile();
  const [selectedUser, setSelectedUser] = useState<ExtendedUser | null>(null);
  const [userProfiles, setUserProfiles] = useState<any[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [userToReset, setUserToReset] = useState<User | null>(null);
  
  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedTeam, setSelectedTeam] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    role: 'berater' as UserRole,
    teamId: '',
    mentorId: '',
  });

  useEffect(() => {
    loadUserProfiles();
    loadTeams();
  }, []);

  const loadUserProfiles = async () => {
    try {
      const profiles = await getUserProfiles();
      setUserProfiles(profiles || []);
    } catch (error) {
      console.error('Error loading user profiles:', error);
    }
  };

  const loadTeams = async () => {
    try {
      const teamsData = await getTeams();
      setTeams(teamsData || []);
    } catch (error) {
      console.error('Error loading teams:', error);
    }
  };

  // Filter and search logic
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const profile = userProfiles.find(p => p.id === user.id);
      const team = teams.find(t => t.id === user.teamId);
      
      // Search filter
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = !searchTerm || 
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.role.toLowerCase().includes(searchLower);
      
      // Role filter
      const matchesRole = selectedRole === 'all' || user.role === selectedRole;
      
      // Team filter
      const matchesTeam = selectedTeam === 'all' || user.teamId === selectedTeam;
      
      // Status filter
      const isActive = profile?.is_active !== false;
      const matchesStatus = selectedStatus === 'all' || 
        (selectedStatus === 'active' && isActive) ||
        (selectedStatus === 'inactive' && !isActive);
      
      return matchesSearch && matchesRole && matchesTeam && matchesStatus;
    });
  }, [users, userProfiles, teams, searchTerm, selectedRole, selectedTeam, selectedStatus]);

  const hasActiveFilters = searchTerm !== '' || selectedRole !== 'all' || 
                          selectedTeam !== 'all' || selectedStatus !== 'all';

  const handleResetFilters = () => {
    setSearchTerm('');
    setSelectedRole('all');
    setSelectedTeam('all');
    setSelectedStatus('all');
  };

  const handleEditUser = (user: User) => {
    const profile = userProfiles.find(p => p.id === user.id);
    setSelectedUser({ ...user, ...profile });
    setIsEditDialogOpen(true);
  };

  const handleViewUser = (user: User) => {
    const profile = userProfiles.find(p => p.id === user.id);
    setSelectedUser({ ...user, ...profile });
    setIsViewDialogOpen(true);
  };

  const handleResetUserStats = (user: User) => {
    setUserToReset(user);
    setIsResetDialogOpen(true);
  };

  const confirmResetUserStats = async () => {
    if (!currentUser || !userToReset) return;

    try {
      await resetUserStatistics(userToReset.id, currentUser.id);
      toast.success(`Statistiken von ${userToReset.name} wurden zurückgesetzt`);
      loadUserProfiles();
    } catch (error) {
      console.error('Error resetting user statistics:', error);
      toast.error('Fehler beim Zurücksetzen der Statistiken');
    } finally {
      setIsResetDialogOpen(false);
      setUserToReset(null);
    }
  };

  const handleUpdateRole = (role: UserRole) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, role });
    }
  };

  const handleUpdateTeam = (teamId: string) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, teamId });
    }
  };

  const handleUpdateMentor = (mentorId: string) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, mentorId });
    }
  };

  const handleUpdateActive = (active: boolean) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, is_active: active });
    }
  };

  const saveUserChanges = async () => {
    if (selectedUser) {
      updateUser(selectedUser);
      
      if (userProfiles.find(p => p.id === selectedUser.id)) {
        try {
          await updateUserProfile(selectedUser.id, {
            team_id: selectedUser.teamId || null,
            is_active: selectedUser.is_active ?? true
          });
        } catch (error) {
          console.error('Error updating user profile:', error);
        }
      }
      
      toast.success(`Benutzer ${selectedUser.name} wurde aktualisiert`);
      setSelectedUser(null);
      setIsEditDialogOpen(false);
      loadUserProfiles();
    }
  };

  const handleCreateUser = () => {
    if (!newUser.name || !newUser.email || !newUser.password) {
      toast.error('Bitte füllen Sie alle Pflichtfelder aus');
      return;
    }

    createUser(newUser);
    toast.success(`Benutzer ${newUser.name} wurde erstellt`);
    setNewUser({
      name: '',
      email: '',
      password: '',
      role: 'berater',
      teamId: '',
      mentorId: '',
    });
    setIsCreateDialogOpen(false);
  };

  const mentors = users.filter(u => u.role === 'mentor');

  if (!currentUser || !canAccessAdmin(currentUser)) {
    return (
      <MainLayout title="Benutzerverwaltung">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
          <div className="text-center">
            <p className="text-gray-600">Sie haben keine Berechtigung, diese Seite anzuzeigen.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Benutzerverwaltung">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
          {/* Header */}
          <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
            <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
              Benutzerverwaltung
            </h1>
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
              Verwalten Sie Benutzer, Rollen und Statistiken
            </p>
          </div>

          {/* Filters and Search */}
          <UserFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            selectedRole={selectedRole}
            onRoleChange={setSelectedRole}
            selectedTeam={selectedTeam}
            onTeamChange={setSelectedTeam}
            selectedStatus={selectedStatus}
            onStatusChange={setSelectedStatus}
            teams={teams}
            filteredCount={filteredUsers.length}
            totalCount={users.length}
            onResetFilters={handleResetFilters}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Create Button */}
          <div className="flex justify-end">
            <CreateUserDialog
              isOpen={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
              newUser={newUser}
              onUpdateNewUser={setNewUser}
              teams={teams}
              mentors={mentors}
              onCreate={handleCreateUser}
            />
          </div>

          {/* Users Table */}
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
            <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
              <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                <Users className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
                Benutzer ({filteredUsers.length})
              </CardTitle>
            </CardHeader>
            <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
              <UserTable
                users={filteredUsers}
                userProfiles={userProfiles}
                teams={teams}
                isMobile={isMobile}
                onViewUser={handleViewUser}
                onEditUser={handleEditUser}
                onResetUserStats={handleResetUserStats}
              />
            </CardContent>
          </Card>

          {/* Dialogs */}
          <UserViewDialog
            isOpen={isViewDialogOpen}
            onOpenChange={setIsViewDialogOpen}
            user={selectedUser}
          />

          <UserEditDialog
            isOpen={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            user={selectedUser}
            teams={teams}
            mentors={mentors}
            onUpdateRole={handleUpdateRole}
            onUpdateTeam={handleUpdateTeam}
            onUpdateMentor={handleUpdateMentor}
            onUpdateActive={handleUpdateActive}
            onSave={saveUserChanges}
            onUpdateUser={updateUser}
          />

          <UserResetDialog
            isOpen={isResetDialogOpen}
            onOpenChange={setIsResetDialogOpen}
            user={userToReset}
            onConfirm={confirmResetUserStats}
          />
        </div>
      </div>
    </MainLayout>
  );
};

export default UserManagementPage;
