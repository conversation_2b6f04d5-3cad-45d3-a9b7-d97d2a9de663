import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { validateAllTouchTargets, TOUCH_TARGET_SIZES } from '@/utils/mobileOptimization';
import { CheckCircle, XCircle, RefreshCw, X } from 'lucide-react';

interface TouchTargetViolation {
  element: HTMLElement;
  currentSize: { width: number; height: number };
  tagName: string;
  className: string;
  id: string;
  textContent: string;
  selector: string;
}

export const ComprehensiveTouchTargetTest: React.FC = () => {
  const [violations, setViolations] = useState<TouchTargetViolation[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] = useState<Date | null>(null);
  const [switchValue, setSwitchValue] = useState(false);

  const runValidation = () => {
    setIsValidating(true);
    
    setTimeout(() => {
      const foundViolations = validateAllTouchTargets();
      setViolations(foundViolations);
      setLastValidation(new Date());
      setIsValidating(false);
    }, 1000);
  };

  useEffect(() => {
    runValidation();
  }, []);

  const getStatusIcon = () => {
    if (violations.length === 0) {
      return <CheckCircle className="h-6 w-6 text-green-600" />;
    }
    return <XCircle className="h-6 w-6 text-red-600" />;
  };

  const getStatusText = () => {
    if (violations.length === 0) {
      return '✅ All touch targets meet 44px minimum requirement';
    }
    return `❌ Found ${violations.length} touch target violations`;
  };

  return (
    <div className="space-y-6 p-4 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            {getStatusIcon()}
            Comprehensive Touch Target Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Status Summary */}
            <div className={`p-4 rounded-lg ${violations.length === 0 ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <p className="font-medium">{getStatusText()}</p>
              {lastValidation && (
                <p className="text-sm text-gray-500 mt-1">
                  Last validated: {lastValidation.toLocaleTimeString()}
                </p>
              )}
            </div>

            {/* Validation Button */}
            <Button 
              onClick={runValidation} 
              disabled={isValidating}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isValidating ? 'animate-spin' : ''}`} />
              {isValidating ? 'Validating...' : 'Run Validation'}
            </Button>

            {/* Test Components Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* Button Tests */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Button Tests</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button size="sm">Small Button (h-10)</Button>
                  <Button size="default">Default Button (h-12)</Button>
                  <Button size="lg">Large Button (h-14)</Button>
                  <Button size="icon">Icon</Button>
                </CardContent>
              </Card>

              {/* Switch Tests */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Switch Tests</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="test-switch" 
                      checked={switchValue}
                      onCheckedChange={setSwitchValue}
                    />
                    <label htmlFor="test-switch">Test Switch</label>
                  </div>
                </CardContent>
              </Card>

              {/* Badge with Close Button Tests */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Badge Tests</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Badge className="flex items-center gap-1">
                    Test Role
                    <button className="ml-1 hover:bg-red-200 rounded-full min-h-[44px] min-w-[44px] flex items-center justify-center">
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                </CardContent>
              </Card>

              {/* Small Button Tests */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Small Button Tests</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex gap-2">
                    <button className="h-6 w-6 bg-gray-200 rounded">6x6</button>
                    <button className="h-7 w-7 bg-gray-200 rounded">7x7</button>
                    <button className="h-8 w-8 bg-gray-200 rounded">8x8</button>
                    <button className="h-10 w-10 bg-gray-200 rounded">10x10</button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Violations Details */}
            {violations.length > 0 && (
              <Card className="border-red-200">
                <CardHeader>
                  <CardTitle className="text-red-600">Touch Target Violations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {violations.map((violation, index) => (
                      <div key={index} className="p-3 border border-red-200 rounded bg-red-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="font-medium text-red-800">{violation.tagName}</p>
                            <p className="text-sm text-red-600">
                              Size: {violation.currentSize.width.toFixed(1)}×{violation.currentSize.height.toFixed(1)}px
                            </p>
                            {violation.id && (
                              <p className="text-xs text-red-500">ID: {violation.id}</p>
                            )}
                            {violation.textContent && (
                              <p className="text-xs text-red-500">Text: "{violation.textContent}"</p>
                            )}
                          </div>
                          <div>
                            <code className="text-xs bg-red-100 px-2 py-1 rounded block break-all">
                              {violation.selector}
                            </code>
                            <p className="text-xs text-red-500 mt-1 break-all">
                              Classes: {violation.className.substring(0, 100)}
                              {violation.className.length > 100 ? '...' : ''}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Success Message */}
            {violations.length === 0 && (
              <Card className="border-green-200 bg-green-50">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                    <div>
                      <p className="text-green-800 font-medium text-lg">
                        🎉 Perfect! All touch targets are mobile-friendly!
                      </p>
                      <p className="text-green-700 text-sm mt-1">
                        All interactive elements meet the 44px minimum requirement for mobile accessibility.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Guidelines */}
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-blue-900">Touch Target Guidelines</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>Minimum:</strong> 44×44px (Apple/Google standard)</li>
                  <li>• <strong>Comfortable:</strong> 48×48px (recommended for primary actions)</li>
                  <li>• <strong>Large:</strong> 56×56px (critical actions)</li>
                  <li>• <strong>Extra Large:</strong> 64×64px (most important actions)</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ComprehensiveTouchTargetTest;
