import { User, UserRole, Permission, BulkRoleAssignment } from '../types';

/**
 * Utility functions for handling multi-role user system
 * Provides backward compatibility with single-role system
 * Enhanced with permission-based access control
 */

// Define role permissions matrix
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  'berater': [
    'user.read',
    'team.read',
    'area.read',
    'data.export'
  ],
  'mentor': [
    'user.read', 'user.write',
    'team.read',
    'area.read',
    'data.export',
    'audit.read'
  ],
  'teamleiter': [
    'user.read', 'user.write',
    'team.read', 'team.write', 'team.assign_members',
    'area.read',
    'data.export',
    'audit.read'
  ],
  'gebietsmanager': [
    'user.read', 'user.write',
    'team.read', 'team.write', 'team.assign_members',
    'area.read', 'area.write', 'area.assign_teams',
    'data.export', 'data.import',
    'audit.read'
  ],
  'admin': [
    'user.read', 'user.write', 'user.delete', 'user.bulk_assign',
    'team.read', 'team.write', 'team.delete', 'team.assign_members',
    'area.read', 'area.write', 'area.delete', 'area.assign_teams',
    'audit.read', 'audit.write', 'audit.export',
    'system.config', 'system.backup', 'system.health',
    'data.export', 'data.import', 'data.backup'
  ]
};

/**
 * Get all roles for a user (handles both single and multi-role users)
 */
export const getUserRoles = (user: User): UserRole[] => {
  if (user.isMultiRole && user.roles && user.roles.length > 0) {
    return user.roles;
  }
  return [user.role];
};

/**
 * Check if user has a specific role
 */
export const hasRole = (user: User, role: UserRole): boolean => {
  const userRoles = getUserRoles(user);
  return userRoles.includes(role);
};

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (user: User, roles: UserRole[]): boolean => {
  const userRoles = getUserRoles(user);
  return roles.some(role => userRoles.includes(role));
};

/**
 * Check if user has all of the specified roles
 */
export const hasAllRoles = (user: User, roles: UserRole[]): boolean => {
  const userRoles = getUserRoles(user);
  return roles.every(role => userRoles.includes(role));
};

/**
 * Get the primary role for a user (for backward compatibility)
 */
export const getPrimaryRole = (user: User): UserRole => {
  return user.role;
};

/**
 * Check if user is multi-role enabled
 */
export const isMultiRoleUser = (user: User): boolean => {
  return user.isMultiRole === true && user.roles && user.roles.length > 1;
};

/**
 * Get available roles for role switching (for multi-role users)
 */
export const getAvailableRoles = (user: User): UserRole[] => {
  if (isMultiRoleUser(user)) {
    return user.roles || [user.role];
  }
  return [user.role];
};

/**
 * Check if user can access admin features
 */
export const canAccessAdmin = (user: User): boolean => {
  return hasRole(user, 'admin');
};

/**
 * Check if user can access management features
 */
export const canAccessManagement = (user: User): boolean => {
  return hasAnyRole(user, ['admin', 'gebietsmanager', 'teamleiter']);
};

/**
 * Check if user can access mentor features
 */
export const canAccessMentor = (user: User): boolean => {
  return hasAnyRole(user, ['admin', 'mentor']);
};

/**
 * Check if user can access berater features
 */
export const canAccessBerater = (user: User): boolean => {
  return hasAnyRole(user, ['admin', 'berater', 'mentor', 'teamleiter', 'gebietsmanager']);
};

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export const getRoleLevel = (role: UserRole): number => {
  const roleLevels: Record<UserRole, number> = {
    'berater': 1,
    'mentor': 2,
    'teamleiter': 3,
    'gebietsmanager': 4,
    'admin': 5
  };
  return roleLevels[role] || 0;
};

/**
 * Get the highest role level for a user
 */
export const getHighestRoleLevel = (user: User): number => {
  const userRoles = getUserRoles(user);
  return Math.max(...userRoles.map(role => getRoleLevel(role)));
};

/**
 * Format roles for display
 */
export const formatRolesForDisplay = (user: User): string => {
  const roles = getUserRoles(user);
  if (roles.length === 1) {
    return formatSingleRole(roles[0]);
  }
  
  return roles.map(role => formatSingleRole(role)).join(' + ');
};

/**
 * Format a single role for display
 */
export const formatSingleRole = (role: UserRole): string => {
  const roleDisplayNames: Record<UserRole, string> = {
    'berater': 'Berater',
    'mentor': 'Mentor',
    'teamleiter': 'Teamleiter',
    'gebietsmanager': 'Gebietsmanager',
    'admin': 'Administrator'
  };
  return roleDisplayNames[role] || role;
};

/**
 * Check if a role can be assigned to a user (business logic)
 */
export const canAssignRole = (currentUser: User, targetRole: UserRole): boolean => {
  // Only admins can assign admin role
  if (targetRole === 'admin') {
    return hasRole(currentUser, 'admin');
  }
  
  // Admins can assign any role
  if (hasRole(currentUser, 'admin')) {
    return true;
  }
  
  // Other role assignment logic can be added here
  return false;
};

/**
 * Validate role combination for multi-role users
 */
export const isValidRoleCombination = (roles: UserRole[]): boolean => {
  // Admin can be combined with any role
  if (roles.includes('admin')) {
    return true;
  }

  // Add other business logic for valid role combinations
  // For now, allow any combination except admin (which is handled above)
  return roles.length > 0;
};

/**
 * Enhanced permission-based access control
 */

/**
 * Get all permissions for a user based on their roles
 */
export const getUserPermissions = (user: User): Permission[] => {
  const userRoles = getUserRoles(user);
  const permissions = new Set<Permission>();

  userRoles.forEach(role => {
    ROLE_PERMISSIONS[role]?.forEach(permission => {
      permissions.add(permission);
    });
  });

  return Array.from(permissions);
};

/**
 * Check if user has a specific permission
 */
export const hasPermission = (user: User, permission: Permission): boolean => {
  const userPermissions = getUserPermissions(user);
  return userPermissions.includes(permission);
};

/**
 * Check if user has any of the specified permissions
 */
export const hasAnyPermission = (user: User, permissions: Permission[]): boolean => {
  const userPermissions = getUserPermissions(user);
  return permissions.some(permission => userPermissions.includes(permission));
};

/**
 * Check if user has all of the specified permissions
 */
export const hasAllPermissions = (user: User, permissions: Permission[]): boolean => {
  const userPermissions = getUserPermissions(user);
  return permissions.every(permission => userPermissions.includes(permission));
};

/**
 * Validate bulk role assignment operation
 */
export const validateBulkRoleAssignment = (
  currentUser: User,
  assignment: BulkRoleAssignment
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check if current user has permission for bulk assignment
  if (!hasPermission(currentUser, 'user.bulk_assign')) {
    errors.push('Sie haben keine Berechtigung für Massenzuweisungen');
  }

  // Check if current user can assign the target role
  if (!canAssignRole(currentUser, assignment.targetRole)) {
    errors.push(`Sie können die Rolle "${assignment.targetRole}" nicht zuweisen`);
  }

  // Validate user IDs
  if (!assignment.userIds || assignment.userIds.length === 0) {
    errors.push('Mindestens ein Benutzer muss ausgewählt werden');
  }

  // Validate expiration date if provided
  if (assignment.expiresAt) {
    const expirationDate = new Date(assignment.expiresAt);
    if (expirationDate <= new Date()) {
      errors.push('Das Ablaufdatum muss in der Zukunft liegen');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Check if user can perform admin actions
 */
export const canPerformAdminAction = (user: User, actionType: string): boolean => {
  const adminPermissions: Record<string, Permission[]> = {
    'role_assignment': ['user.write', 'user.bulk_assign'],
    'user_activation': ['user.write'],
    'team_assignment': ['team.assign_members'],
    'system_config': ['system.config'],
    'data_export': ['data.export'],
    'data_import': ['data.import'],
    'data_backup': ['data.backup']
  };

  const requiredPermissions = adminPermissions[actionType];
  if (!requiredPermissions) {
    return false;
  }

  return hasAnyPermission(user, requiredPermissions);
};
