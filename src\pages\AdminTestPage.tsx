import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';
import { 
  getAreas, 
  createArea, 
  getTeams, 
  getAuditLogs, 
  getUserProfiles 
} from '@/integrations/supabase/functions';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Database,
  Users,
  Building,
  Shield,
  Loader2
} from 'lucide-react';

const AdminTestPage: React.FC = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isRunning, setIsRunning] = useState(false);
  const [touchTargetViolations, setTouchTargetViolations] = useState(0);

  const runSupabaseTests = async () => {
    setIsRunning(true);
    const results: Record<string, any> = {};

    try {
      // Test Areas
      console.log('Testing Areas...');
      const areas = await getAreas();
      results.areas = { success: true, count: areas?.length || 0, data: areas };
      
      // Test creating an area
      const newArea = await createArea({
        name: `Test Area ${Date.now()}`,
        description: 'Test area for validation',
        postal_codes: ['12345', '67890']
      });
      results.createArea = { success: true, data: newArea };

    } catch (error) {
      console.error('Areas test failed:', error);
      results.areas = { success: false, error: error.message };
    }

    try {
      // Test Teams
      console.log('Testing Teams...');
      const teams = await getTeams();
      results.teams = { success: true, count: teams?.length || 0, data: teams };
    } catch (error) {
      console.error('Teams test failed:', error);
      results.teams = { success: false, error: error.message };
    }

    try {
      // Test Audit Logs
      console.log('Testing Audit Logs...');
      const auditLogs = await getAuditLogs(10);
      results.auditLogs = { success: true, count: auditLogs?.length || 0, data: auditLogs };
    } catch (error) {
      console.error('Audit logs test failed:', error);
      results.auditLogs = { success: false, error: error.message };
    }

    try {
      // Test User Profiles
      console.log('Testing User Profiles...');
      const userProfiles = await getUserProfiles();
      results.userProfiles = { success: true, count: userProfiles?.length || 0, data: userProfiles };
    } catch (error) {
      console.error('User profiles test failed:', error);
      results.userProfiles = { success: false, error: error.message };
    }

    setTestResults(results);
    setIsRunning(false);
    
    const successCount = Object.values(results).filter(r => r.success).length;
    const totalCount = Object.keys(results).length;
    
    if (successCount === totalCount) {
      toast.success(`All ${totalCount} Supabase tests passed! Using fallback data.`);
    } else {
      toast.warning(`${successCount}/${totalCount} tests passed. Some functions may be using fallback data.`);
    }
  };

  const checkTouchTargets = () => {
    const buttons = document.querySelectorAll('button, [role="button"], input, select, textarea');
    let violations = 0;
    
    buttons.forEach((element) => {
      const rect = element.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(element);
      const minHeight = parseInt(computedStyle.minHeight) || rect.height;
      const minWidth = parseInt(computedStyle.minWidth) || rect.width;
      
      if (minHeight < 44 || minWidth < 44) {
        violations++;
        console.warn('Touch target violation:', element, { height: minHeight, width: minWidth });
      }
    });
    
    setTouchTargetViolations(violations);
    
    if (violations === 0) {
      toast.success('All touch targets meet the 44px minimum requirement!');
    } else {
      toast.error(`Found ${violations} touch target violations`);
    }
  };

  useEffect(() => {
    // Run touch target check on mount
    setTimeout(checkTouchTargets, 1000);
  }, []);

  const TestResultCard: React.FC<{ title: string; result: any }> = ({ title, result }) => (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          {result?.success ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <XCircle className="h-5 w-5 text-red-600" />
          )}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {result?.success ? (
          <div className="space-y-2">
            <Badge variant="default" className="bg-green-100 text-green-800">
              Success
            </Badge>
            {result.count !== undefined && (
              <p className="text-sm text-gray-600">
                Found {result.count} records
              </p>
            )}
            {result.data && (
              <details className="text-xs">
                <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                  View Data
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto max-h-32">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </details>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <Badge variant="destructive">
              Failed
            </Badge>
            {result?.error && (
              <p className="text-sm text-red-600">
                Error: {result.error}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (!user) {
    return (
      <MainLayout title="Admin Test">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Authentication Required</h3>
            <p className="text-gray-600">Please log in to access the admin test page.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Admin Test">
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Functionality Test</h1>
          <p className="text-gray-600">
            Test Supabase integration and touch target compliance
          </p>
        </div>

        {/* Test Controls */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-600" />
              Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4 flex-wrap">
              <Button 
                onClick={runSupabaseTests} 
                disabled={isRunning}
                className="min-h-[44px]"
              >
                {isRunning ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Database className="h-4 w-4 mr-2" />
                    Test Supabase Functions
                  </>
                )}
              </Button>
              
              <Button 
                onClick={checkTouchTargets} 
                variant="outline"
                className="min-h-[44px]"
              >
                <Shield className="h-4 w-4 mr-2" />
                Check Touch Targets
              </Button>
            </div>

            {/* Touch Target Status */}
            <div className="p-4 rounded-lg border bg-gray-50">
              <div className="flex items-center gap-2 mb-2">
                {touchTargetViolations === 0 ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className="font-medium">Touch Target Compliance</span>
              </div>
              <p className="text-sm text-gray-600">
                {touchTargetViolations === 0 
                  ? 'All interactive elements meet the 44px minimum requirement'
                  : `${touchTargetViolations} elements are smaller than 44px`
                }
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
            
            <TestResultCard title="Areas Management" result={testResults.areas} />
            <TestResultCard title="Area Creation" result={testResults.createArea} />
            <TestResultCard title="Teams Management" result={testResults.teams} />
            <TestResultCard title="Audit Logs" result={testResults.auditLogs} />
            <TestResultCard title="User Profiles" result={testResults.userProfiles} />
          </div>
        )}

        {/* Sample Touch Target Elements */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              Sample Touch Target Elements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button size="sm" className="min-h-[44px]">Small Button</Button>
              <Button variant="outline" className="min-h-[44px]">Outline</Button>
              <Button variant="ghost" className="min-h-[44px]">Ghost</Button>
              <Button size="icon" className="min-h-[44px] min-w-[44px]">
                <Users className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex gap-2">
              <button className="p-2 border rounded hover:bg-gray-50 min-h-[44px] min-w-[44px]">
                <Building className="h-4 w-4" />
              </button>
              <button className="p-3 border rounded hover:bg-gray-50 min-h-[44px] min-w-[44px]">
                <Shield className="h-4 w-4" />
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default AdminTestPage;
