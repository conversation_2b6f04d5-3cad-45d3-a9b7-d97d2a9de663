import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { SystemHealth } from '@/types';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Activity,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock,
  Users,
  Zap,
  TrendingUp
} from 'lucide-react';

interface SystemHealthMonitorProps {
  health: SystemHealth | null;
  detailed?: boolean;
}

export const SystemHealthMonitor: React.FC<SystemHealthMonitorProps> = ({ 
  health, 
  detailed = false 
}) => {
  const isMobile = useIsMobile();

  if (!health) {
    return (
      <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-gray-400" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
            <p className="text-gray-500">Lade Systemstatus...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getProgressColor = (value: number, isInverted = false) => {
    if (isInverted) {
      // For error rates, lower is better
      if (value <= 1) return 'bg-green-500';
      if (value <= 5) return 'bg-yellow-500';
      return 'bg-red-500';
    } else {
      // For uptime, memory, etc., higher is generally better
      if (value >= 95) return 'bg-green-500';
      if (value >= 80) return 'bg-yellow-500';
      return 'bg-red-500';
    }
  };

  const metrics = [
    {
      label: 'Verfügbarkeit',
      value: health.uptime,
      unit: '%',
      icon: TrendingUp,
      description: 'System-Verfügbarkeit der letzten 30 Tage'
    },
    {
      label: 'Speichernutzung',
      value: health.memoryUsage,
      unit: '%',
      icon: Activity,
      description: 'Aktuelle RAM-Auslastung'
    },
    {
      label: 'Aktive Benutzer',
      value: health.activeUsers,
      unit: '',
      icon: Users,
      description: 'Derzeit angemeldete Benutzer'
    },
    {
      label: 'Antwortzeit',
      value: health.responseTime,
      unit: 'ms',
      icon: Zap,
      description: 'Durchschnittliche API-Antwortzeit'
    }
  ];

  return (
    <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon(health.status)}
          System Health Monitor
        </CardTitle>
        <CardDescription>
          Letzte Aktualisierung: {new Date(health.lastChecked).toLocaleString('de-DE')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Status */}
        <div className={`p-4 rounded-lg border ${getStatusColor(health.status)}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Systemstatus</h3>
              <p className="text-sm opacity-80">
                {health.status === 'healthy' && 'Alle Systeme funktionieren normal'}
                {health.status === 'warning' && 'Einige Systeme benötigen Aufmerksamkeit'}
                {health.status === 'critical' && 'Kritische Probleme erkannt'}
              </p>
            </div>
            <Badge 
              variant={health.status === 'healthy' ? 'default' : 'destructive'}
              className="capitalize"
            >
              {health.status === 'healthy' ? 'Gesund' : 
               health.status === 'warning' ? 'Warnung' : 'Kritisch'}
            </Badge>
          </div>
        </div>

        {/* Metrics Grid */}
        <div className={`grid ${detailed ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-2'} gap-4`}>
          {metrics.map((metric, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <metric.icon className="h-4 w-4 text-gray-600" />
                  <span className={`font-medium ${isMobile ? 'text-sm' : 'text-base'}`}>
                    {metric.label}
                  </span>
                </div>
                <span className={`font-bold ${isMobile ? 'text-sm' : 'text-base'}`}>
                  {metric.value}{metric.unit}
                </span>
              </div>
              
              {(metric.label === 'Verfügbarkeit' || metric.label === 'Speichernutzung') && (
                <div className="space-y-1">
                  <Progress 
                    value={metric.value} 
                    className="h-2"
                  />
                  {detailed && (
                    <p className="text-xs text-gray-500">{metric.description}</p>
                  )}
                </div>
              )}
              
              {metric.label === 'Antwortzeit' && detailed && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Ziel: &lt;250ms</span>
                    <span className={metric.value <= 250 ? 'text-green-600' : 'text-red-600'}>
                      {metric.value <= 250 ? '✓ Gut' : '⚠ Langsam'}
                    </span>
                  </div>
                  <Progress 
                    value={Math.min((metric.value / 500) * 100, 100)} 
                    className="h-2"
                  />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Error Rate */}
        {detailed && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-gray-600" />
                <span className="font-medium">Fehlerrate</span>
              </div>
              <span className="font-bold">{health.errorRate}%</span>
            </div>
            <div className="space-y-1">
              <Progress 
                value={Math.min(health.errorRate * 20, 100)} 
                className="h-2"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>Ziel: &lt;1%</span>
                <span className={health.errorRate <= 1 ? 'text-green-600' : 'text-red-600'}>
                  {health.errorRate <= 1 ? '✓ Niedrig' : '⚠ Hoch'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {detailed && (
          <div className="pt-4 border-t">
            <h4 className="font-medium mb-3">Schnellaktionen</h4>
            <div className="grid grid-cols-2 gap-2">
              <button className="p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback">
                System neustarten
              </button>
              <button className="p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback">
                Cache leeren
              </button>
              <button className="p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback">
                Logs exportieren
              </button>
              <button className="p-3 text-sm border rounded-lg hover:bg-gray-50 transition-colors min-h-[44px] min-w-[44px] touch-feedback">
                Backup erstellen
              </button>
            </div>
          </div>
        )}

        {/* Status History */}
        {detailed && (
          <div className="pt-4 border-t">
            <h4 className="font-medium mb-3">Status-Verlauf (24h)</h4>
            <div className="flex gap-1">
              {Array.from({ length: 24 }, (_, i) => {
                const isHealthy = Math.random() > 0.1; // 90% uptime simulation
                return (
                  <div
                    key={i}
                    className={`h-8 flex-1 rounded-sm ${
                      isHealthy ? 'bg-green-200' : 'bg-red-200'
                    }`}
                    title={`${23 - i} Stunden ago: ${isHealthy ? 'Gesund' : 'Problem'}`}
                  />
                );
              })}
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>24h ago</span>
              <span>Jetzt</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
