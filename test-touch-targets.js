// Simple test script to verify touch target fixes
// Run this in browser console to test the validation

console.log('🔍 Testing Touch Target Validation...');

// Import the validation function (this would work in the actual app context)
const testTouchTargets = () => {
  // Simulate the validation logic
  const interactiveElements = document.querySelectorAll(
    'button, input, select, textarea, [role="button"], [role="tab"], a, [tabindex]:not([tabindex="-1"]), .touch-target'
  );

  const violations = [];
  const MINIMUM_SIZE = 44;

  interactiveElements.forEach((element) => {
    const htmlElement = element;
    
    // Skip non-interactive content containers
    const nonInteractiveSelectors = [
      '[data-radix-tabs-content]',
      '[data-radix-popover-content]',
      '[data-radix-dialog-content]',
      '[data-radix-select-content]',
      '[data-radix-tooltip-content]',
      '[data-radix-hover-card-content]',
      '[data-radix-menubar-content]',
      '[data-radix-dropdown-menu-content]',
      '[data-radix-context-menu-content]'
    ];
    
    // Check if element matches any non-interactive selector
    for (const selector of nonInteractiveSelectors) {
      if (element.matches(selector)) {
        console.log('⏭️ Skipping non-interactive container:', element.id || element.className);
        return;
      }
    }
    
    // Skip elements with 0x0 dimensions
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) {
      console.log('⏭️ Skipping 0x0 element:', element.id || element.className);
      return;
    }
    
    // Skip elements that are not visible
    if (element.offsetParent === null && element.style.position !== 'fixed') {
      console.log('⏭️ Skipping hidden element:', element.id || element.className);
      return;
    }
    
    // Check touch target size
    if (rect.width < MINIMUM_SIZE || rect.height < MINIMUM_SIZE) {
      violations.push({
        element: htmlElement,
        size: { width: rect.width, height: rect.height },
        tagName: htmlElement.tagName,
        id: htmlElement.id,
        className: htmlElement.className
      });
    }
  });

  return violations;
};

// Run the test
const violations = testTouchTargets();

console.log('\n📊 Touch Target Validation Results:');
console.log('=====================================');

if (violations.length === 0) {
  console.log('✅ SUCCESS: All touch targets meet the 44px minimum requirement!');
  console.log('🎉 No violations found - mobile accessibility compliance achieved!');
} else {
  console.log(`❌ VIOLATIONS FOUND: ${violations.length} touch targets are too small`);
  console.table(violations.map(v => ({
    tagName: v.tagName,
    id: v.id || 'N/A',
    width: Math.round(v.size.width),
    height: Math.round(v.size.height),
    className: v.className.substring(0, 50) + (v.className.length > 50 ? '...' : '')
  })));
}

console.log('\n🔧 Test completed. Check the results above.');
console.log('💡 If violations remain, they may be legitimate interactive elements that need fixing.');

// Return results for further inspection
window.touchTargetTestResults = {
  violations,
  totalElements: document.querySelectorAll('button, input, select, textarea, [role="button"], [role="tab"], a, [tabindex]:not([tabindex="-1"]), .touch-target').length,
  passed: violations.length === 0
};
