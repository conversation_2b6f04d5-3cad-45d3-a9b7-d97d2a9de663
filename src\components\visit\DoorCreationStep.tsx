
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DoorCreation } from './DoorCreation';
import { AddressHeader } from './AddressHeader';
import { Address, House } from '@/types';

interface DoorCreationStepProps {
  visitId: string;
  address: Address;
  house: House;
  onCreateDoor: (doorName: string) => void;
  isCreating: boolean;
}

export const DoorCreationStep: React.FC<DoorCreationStepProps> = ({
  visitId,
  address,
  house,
  onCreateDoor,
  isCreating
}) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <AddressHeader 
        address={address} 
        house={house} 
        step={1} 
      />

      <DoorCreation
        onCreateDoor={onCreateDoor}
        isCreating={isCreating}
      />
    </div>
  );
};
