import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { DownloadIcon, TrendingUp } from 'lucide-react';
import { useData } from '@/context/data';
import { Badge } from '@/components/ui/badge';
import { Door, VisitStatus } from '@/types';
import VisitRecommendations from '@/components/visit-recommendations/VisitRecommendations';

const DailyView: React.FC = () => {
  const navigate = useNavigate();
  const {
    getTodaysHouses,
    getVisitsByHouse,
    getDoorsByVisit,
    getAddressById,
    getProductsByDoor,
    getAddressesRequiringReturnVisits
  } = useData();
  
  const todaysHouses = getTodaysHouses();
  const addressesRequiringVisits = getAddressesRequiringReturnVisits();
  
  // Group houses by street for a cleaner UI
  const housesByStreet: Record<string, { addressId: string; houses: typeof todaysHouses }> = {};
  
  todaysHouses.forEach(house => {
    const address = getAddressById(house.addressId);
    if (address) {
      const streetName = address.street;
      if (!housesByStreet[streetName]) {
        housesByStreet[streetName] = { 
          addressId: house.addressId,
          houses: []
        };
      }
      housesByStreet[streetName].houses.push(house);
    }
  });

  // Get all doors with their status
  const getAllDoors = (houseId: string): Door[] => {
    const visits = getVisitsByHouse(houseId);
    return visits.flatMap(visit => getDoorsByVisit(visit.id));
  };

  // Get status badge color
  const getStatusBadge = (status: VisitStatus) => {
    switch(status) {
      case 'N/A':
        return <Badge variant="outline" className="bg-gray-100">N/A</Badge>;
      case 'Angetroffen → Termin':
        return <Badge className="bg-yellow-500">Termin</Badge>;
      case 'Angetroffen → Kein Interesse':
        return <Badge variant="destructive">Kein Interesse</Badge>;
      case 'Angetroffen → Sale':
        return <Badge className="bg-green-500">Sale</Badge>;
      default:
        return <Badge variant="outline">Unbekannt</Badge>;
    }
  };

  // Download functionality
  const handleDownload = () => {
    try {
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];
      
      // Calculate summary statistics
      const allDoors = todaysHouses.flatMap(house => getAllDoors(house.id));
      const salesCount = allDoors.filter(d => d.status === 'Angetroffen → Sale').length;
      const termineCount = allDoors.filter(d => d.status === 'Angetroffen → Termin').length;
      const keinInteresseCount = allDoors.filter(d => d.status === 'Angetroffen → Kein Interesse').length;
      const naCount = allDoors.filter(d => d.status === 'N/A').length;

      // Structure the data
      const exportData = {
        datum: todayString,
        exportZeitpunkt: new Date().toISOString(),
        zusammenfassung: {
          gesamtHaeuser: todaysHouses.length,
          gesamtTueren: allDoors.length,
          sales: salesCount,
          termine: termineCount,
          keinInteresse: keinInteresseCount,
          nichtAngetroffen: naCount
        },
        strassen: Object.entries(housesByStreet).map(([streetName, { houses }]) => ({
          strassenname: streetName,
          anzahlHaeuser: houses.length,
          haeuser: houses.map(house => {
            const address = getAddressById(house.addressId);
            const doors = getAllDoors(house.id);
            
            return {
              hausnummer: house.houseNumber,
              vollAdresse: address ? `${address.street} ${house.houseNumber}, ${address.zipCode} ${address.city}` : 'Unbekannte Adresse',
              typ: house.type,
              anzahlTueren: doors.length,
              tueren: doors.map(door => {
                const products = getProductsByDoor(door.id);
                return {
                  name: door.name,
                  stockwerk: door.floor || null,
                  status: door.status,
                  notizen: door.notes || null,
                  anzahlProdukte: products.length,
                  produkte: products.map(product => ({
                    kategorie: product.category,
                    typ: product.type,
                    anzahl: product.quantity,
                    notizen: product.notes || null
                  }))
                };
              }),
              statistiken: {
                sales: doors.filter(d => d.status === 'Angetroffen → Sale').length,
                termine: doors.filter(d => d.status === 'Angetroffen → Termin').length,
                keinInteresse: doors.filter(d => d.status === 'Angetroffen → Kein Interesse').length,
                nichtAngetroffen: doors.filter(d => d.status === 'N/A').length
              }
            };
          })
        }))
      };

      // Create and download the file
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `tagesübersicht-${todayString}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the URL object
      URL.revokeObjectURL(link.href);
      
    } catch (error) {
      console.error('Fehler beim Download:', error);
      alert('Fehler beim Erstellen der Download-Datei. Bitte versuchen Sie es erneut.');
    }
  };

  return (
    <div className="w-full mobile-container mobile-spacing">
      <div className="flex justify-between items-center mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold">Tagesübersicht</h2>
        <Button
          variant="outline"
          size="icon"
          onClick={handleDownload}
          className="mobile-button h-12 w-12 touch-feedback"
          data-primary="true"
        >
          <DownloadIcon size={20} />
        </Button>
      </div>

      {/* Visit Recommendations Section */}
      {addressesRequiringVisits.length > 0 && (
        <div>
          <VisitRecommendations />
        </div>
      )}

      {/* Today's Visits Section */}
      <div>
        {Object.keys(housesByStreet).length === 0 ? (
          <Card className="mobile-card w-full">
            <CardHeader className="p-4 md:p-6">
              <CardTitle className="text-lg md:text-xl">Heutige Besuche</CardTitle>
            </CardHeader>
            <CardContent className="p-4 md:p-6">
              <div className="text-center py-6 md:py-8">
                <p className="text-muted-foreground mb-4 text-sm md:text-base">Heute wurden noch keine Besuche erfasst</p>
                <Button
                  onClick={() => navigate('/')}
                  className="mobile-action-button bg-red-600 hover:bg-red-700 text-white touch-feedback"
                  data-size="large"
                >
                  Neuen Besuch erfassen
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="mobile-card w-full">
            <CardHeader className="p-4 md:p-6">
              <CardTitle className="text-lg md:text-xl">Heutige Besuche</CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
            {Object.entries(housesByStreet).map(([street, { addressId, houses }]) => {
              const address = getAddressById(addressId);
              return (
                <AccordionItem key={street} value={street}>
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex justify-between w-full pr-4">
                      <span>{street}</span>
                      <Badge variant="outline" className="ml-2">
                        {houses.length} {houses.length === 1 ? 'Haus' : 'Häuser'}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2 pl-2">
                      {houses.map(house => {
                        const doors = getAllDoors(house.id);
                        const salesCount = doors.filter(d => d.status === 'Angetroffen → Sale').length;
                        
                        return (
                          <Accordion key={house.id} type="single" collapsible className="w-full">
                            <AccordionItem value={house.id}>
                              <AccordionTrigger className="py-2 hover:no-underline">
                                <div className="flex justify-between w-full pr-4">
                                  <span>
                                    {address?.street} {house.houseNumber}
                                    {house.type === 'MFH' && (
                                      <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                                        MFH
                                      </span>
                                    )}
                                  </span>
                                  {salesCount > 0 && (
                                    <Badge className="bg-green-500 ml-2">
                                      {salesCount} {salesCount === 1 ? 'Sale' : 'Sales'}
                                    </Badge>
                                  )}
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="pl-4 pt-2 space-y-2">
                                  {doors.length === 0 ? (
                                    <p className="text-sm text-muted-foreground">Keine Türen erfasst</p>
                                  ) : (
                                    doors.map(door => (
                                      <div key={door.id} className="flex justify-between items-center text-sm border-b pb-2">
                                        <div>
                                          <span className="font-medium">{door.name}</span>
                                          {door.floor && <span className="text-muted-foreground ml-2">({door.floor})</span>}
                                        </div>
                                        {getStatusBadge(door.status)}
                                      </div>
                                    ))
                                  )}
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          </Accordion>
                        );
                      })}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              );
            })}
              </Accordion>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default DailyView;
