import React, { useState } from 'react';
import { User, UserRole } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { UserCog, Plus, X, Shield } from 'lucide-react';
import { 
  getUserRoles, 
  formatSingleRole, 
  isMultiRoleUser, 
  canAssignRole,
  isValidRoleCombination 
} from '@/utils/roleUtils';

interface UserRoleManagerProps {
  user: User;
  currentUser: User;
  onUpdateUser: (updatedUser: User) => void;
}

export const UserRoleManager: React.FC<UserRoleManagerProps> = ({
  user,
  currentUser,
  onUpdateUser
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | ''>('');
  const [enableMultiRole, setEnableMultiRole] = useState(user.isMultiRole || false);
  const [tempRoles, setTempRoles] = useState<UserRole[]>(getUserRoles(user));

  const allRoles: UserRole[] = ['berater', 'mentor', 'teamleiter', 'gebietsmanager', 'admin'];
  const userRoles = getUserRoles(user);
  const isUserMultiRole = isMultiRoleUser(user);

  const handleAddRole = () => {
    if (!selectedRole) return;

    const newRoles = [...tempRoles];
    if (!newRoles.includes(selectedRole)) {
      newRoles.push(selectedRole);
      
      if (isValidRoleCombination(newRoles)) {
        setTempRoles(newRoles);
        setSelectedRole('');
      } else {
        toast.error('Diese Rollenkombination ist nicht gültig');
      }
    } else {
      toast.error('Diese Rolle ist bereits zugewiesen');
    }
  };

  const handleRemoveRole = (roleToRemove: UserRole) => {
    if (tempRoles.length <= 1) {
      toast.error('Mindestens eine Rolle muss zugewiesen bleiben');
      return;
    }

    setTempRoles(tempRoles.filter(role => role !== roleToRemove));
  };

  const handleSave = () => {
    if (tempRoles.length === 0) {
      toast.error('Mindestens eine Rolle muss zugewiesen sein');
      return;
    }

    // Check if current user can assign all selected roles
    const canAssignAll = tempRoles.every(role => canAssignRole(currentUser, role));
    if (!canAssignAll) {
      toast.error('Sie haben nicht die Berechtigung, alle ausgewählten Rollen zuzuweisen');
      return;
    }

    const updatedUser: User = {
      ...user,
      role: tempRoles[0], // Primary role (first in array)
      roles: enableMultiRole && tempRoles.length > 1 ? tempRoles : undefined,
      isMultiRole: enableMultiRole && tempRoles.length > 1
    };

    onUpdateUser(updatedUser);
    setIsEditing(false);
    toast.success('Benutzerrollen wurden erfolgreich aktualisiert');
  };

  const handleCancel = () => {
    setTempRoles(getUserRoles(user));
    setEnableMultiRole(user.isMultiRole || false);
    setSelectedRole('');
    setIsEditing(false);
  };

  const availableRoles = allRoles.filter(role => 
    !tempRoles.includes(role) && canAssignRole(currentUser, role)
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <UserCog className="h-5 w-5 text-blue-600" />
          Rollenverwaltung
          {isUserMultiRole && (
            <Badge variant="secondary" className="ml-2">
              <Shield className="h-3 w-3 mr-1" />
              Multi-Role
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isEditing ? (
          // Display Mode
          <div className="space-y-3">
            <div>
              <Label className="text-sm font-medium text-gray-700">
                Aktuelle Rollen:
              </Label>
              <div className="flex flex-wrap gap-2 mt-1">
                {userRoles.map((role) => (
                  <Badge 
                    key={role} 
                    variant={role === user.role ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {formatSingleRole(role)}
                    {role === user.role && (
                      <span className="ml-1 text-xs opacity-75">(Primär)</span>
                    )}
                  </Badge>
                ))}
              </div>
            </div>
            
            {isUserMultiRole && (
              <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
                <Shield className="h-3 w-3 inline mr-1" />
                Dieser Benutzer kann zwischen verschiedenen Rollen wechseln
              </div>
            )}

            <Button 
              onClick={() => setIsEditing(true)}
              variant="outline"
              size="sm"
              className="w-full"
            >
              Rollen bearbeiten
            </Button>
          </div>
        ) : (
          // Edit Mode
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="multi-role"
                checked={enableMultiRole}
                onCheckedChange={setEnableMultiRole}
              />
              <Label htmlFor="multi-role" className="text-sm">
                Multi-Role aktivieren
              </Label>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Zugewiesene Rollen:
              </Label>
              <div className="flex flex-wrap gap-2 mt-1 min-h-[2rem] p-2 border rounded-md bg-gray-50">
                {tempRoles.length === 0 ? (
                  <span className="text-gray-400 text-sm">Keine Rollen zugewiesen</span>
                ) : (
                  tempRoles.map((role, index) => (
                    <Badge 
                      key={role} 
                      variant={index === 0 ? "default" : "secondary"}
                      className="text-xs flex items-center gap-1"
                    >
                      {formatSingleRole(role)}
                      {index === 0 && (
                        <span className="text-xs opacity-75">(Primär)</span>
                      )}
                      {tempRoles.length > 1 && (
                        <button
                          onClick={() => handleRemoveRole(role)}
                          className="ml-1 hover:bg-red-200 rounded-full min-h-[44px] min-w-[44px] flex items-center justify-center"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </Badge>
                  ))
                )}
              </div>
            </div>

            {enableMultiRole && availableRoles.length > 0 && (
              <div className="flex gap-2">
                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Rolle hinzufügen..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map((role) => (
                      <SelectItem key={role} value={role}>
                        {formatSingleRole(role)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button 
                  onClick={handleAddRole}
                  disabled={!selectedRole}
                  size="sm"
                  variant="outline"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            )}

            <div className="flex gap-2 pt-2">
              <Button onClick={handleSave} size="sm" className="flex-1">
                Speichern
              </Button>
              <Button onClick={handleCancel} variant="outline" size="sm" className="flex-1">
                Abbrechen
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
