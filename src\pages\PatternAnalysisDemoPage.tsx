import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import SmartVisitAssistant from '@/components/smart-visit-assistant/SmartVisitAssistant';
import PersonalizedDashboard from '@/components/personalized-dashboard/PersonalizedDashboard';
import EnhancedVisitTracker from '@/components/enhanced-visit-tracker/EnhancedVisitTracker';
import VisitRecommendations from '@/components/visit-recommendations/VisitRecommendations';
import PatternAnalysisDemo from '@/components/demo/PatternAnalysisDemo';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Brain,
  User,
  MapPin,
  TrendingUp,
  Zap,
  Settings,
  BarChart3
} from 'lucide-react';

const SmartVisitAssistantPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('assistant');

  return (
    <MainLayout title="Smart Visit Assistant">
      <div className="w-full space-y-6 p-4">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2">
            <Brain className="h-8 w-8 text-blue-500" />
            <h1 className="text-3xl font-bold">Smart Visit Assistant</h1>
          </div>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Ihr intelligenter Begleiter für optimierte Besuche. KI-gestützte Empfehlungen,
            detaillierte Übersichten und personalisierte Insights für maximalen Erfolg.
          </p>
        </div>

        {/* Main Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="assistant" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              <span className="hidden sm:inline">Assistent</span>
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="tracker" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span className="hidden sm:inline">Übersicht</span>
            </TabsTrigger>
            <TabsTrigger value="insights" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              <span className="hidden sm:inline">Insights</span>
            </TabsTrigger>
            <TabsTrigger value="demo" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Demo</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="assistant" className="mt-6">
            <SmartVisitAssistant />
          </TabsContent>

          <TabsContent value="dashboard" className="mt-6">
            <PersonalizedDashboard />
          </TabsContent>

          <TabsContent value="tracker" className="mt-6">
            <EnhancedVisitTracker />
          </TabsContent>

          <TabsContent value="insights" className="mt-6">
            <VisitRecommendations />
          </TabsContent>

          <TabsContent value="demo" className="mt-6">
            <PatternAnalysisDemo />
          </TabsContent>
        </Tabs>

        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 mb-3">
                <Brain className="h-6 w-6 text-blue-600" />
                <span className="font-semibold text-blue-800">KI-Empfehlungen</span>
              </div>
              <p className="text-sm text-blue-700">
                Intelligente Zeitfenster-Vorhersagen basierend auf historischen Mustern und Erfolgsraten
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 mb-3">
                <MapPin className="h-6 w-6 text-green-600" />
                <span className="font-semibold text-green-800">GPS-Tracking</span>
              </div>
              <p className="text-sm text-green-700">
                Automatische Besuchserfassung und Navigation mit präziser Standorterkennung
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 mb-3">
                <Zap className="h-6 w-6 text-purple-600" />
                <span className="font-semibold text-purple-800">Gamification</span>
              </div>
              <p className="text-sm text-purple-700">
                Erfolge, Streaks und Levelaufstieg für motivierende Leistungssteigerung
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default SmartVisitAssistantPage;
