
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/context/auth';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, User, Lock, Zap, AlertCircle } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import FormField from './FormField';
import { LoginFormState, ValidationError } from '@/context/auth/types';
import { validateLoginCredentials, createFormValidator } from '@/utils/validation';
import { cn } from '@/lib/utils';

interface LoginFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  email,
  setEmail,
  password,
  setPassword
}) => {
  const navigate = useNavigate();
  const { login, isLoading, error, clearError } = useAuth();
  const [showAccountSwitcher, setShowAccountSwitcher] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [formState, setFormState] = useState<LoginFormState>({
    emailOrName: email,
    password: password,
    rememberMe: false,
    isSubmitting: false,
  });

  // Form validator
  const validator = createFormValidator();

  // Sync props with internal state
  useEffect(() => {
    setFormState(prev => ({
      ...prev,
      emailOrName: email,
      password: password,
    }));
  }, [email, password]);

  // Handle field changes with validation
  const handleEmailChange = (value: string) => {
    setEmail(value);
    setFormState(prev => ({ ...prev, emailOrName: value }));
    clearError();

    // Clear field error when user starts typing
    if (validator.getFieldError('emailOrName')) {
      validator.clearError('emailOrName');
    }
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    setFormState(prev => ({ ...prev, password: value }));
    clearError();

    // Clear field error when user starts typing
    if (validator.getFieldError('password')) {
      validator.clearError('password');
    }
  };

  // Handle field blur for validation
  const handleFieldBlur = (field: string) => {
    validator.setTouched(field);

    if (field === 'emailOrName' || field === 'password') {
      const errors = validateLoginCredentials(formState.emailOrName, formState.password);
      errors.forEach(error => {
        if (error.field === field) {
          validator.setError(error);
        }
      });
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Mark all fields as touched
    validator.setTouched('emailOrName');
    validator.setTouched('password');

    // Validate form
    const errors = validateLoginCredentials(formState.emailOrName, formState.password);
    if (errors.length > 0) {
      errors.forEach(error => validator.setError(error));
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      const result = await login(formState.emailOrName, formState.password, rememberMe);
      if (result.success) {
        navigate('/');
      }
      // Error handling is now managed by the auth provider
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  };

  const handleQuickLogin = async (role: string) => {
    let accountEmail = '';
    const accountPassword = 'test123';

    switch(role) {
      case 'berater':
        accountEmail = '<EMAIL>';
        break;
      case 'mentor':
        accountEmail = '<EMAIL>';
        break;
      case 'teamleiter':
        accountEmail = '<EMAIL>';
        break;
      case 'manager':
        accountEmail = '<EMAIL>';
        break;
      case 'admin':
        accountEmail = '<EMAIL>';
        break;
      default:
        accountEmail = '<EMAIL>';
    }

    setEmail(accountEmail);
    setPassword(accountPassword);

    try {
      const success = await login(accountEmail, accountPassword);
      if (success) {
        navigate('/');
      }
    } catch (error) {
      console.error('Quick login error:', error);
    }
  };

  const testAccounts = [
    { role: 'berater', label: 'Berater', icon: User, color: 'from-blue-500 to-blue-600' },
    { role: 'mentor', label: 'Mentor', icon: Zap, color: 'from-green-500 to-green-600' },
    { role: 'teamleiter', label: 'Teamleiter', icon: User, color: 'from-purple-500 to-purple-600' },
    { role: 'manager', label: 'Manager', icon: User, color: 'from-orange-500 to-orange-600' },
    { role: 'admin', label: 'Admin', icon: Lock, color: 'from-red-500 to-red-600' },
  ];

  return (
    <form onSubmit={handleLogin} className="space-y-6">
      {/* Test Account Switcher */}
      <Collapsible open={showAccountSwitcher} onOpenChange={setShowAccountSwitcher}>
        <CollapsibleTrigger asChild>
          <Button
            type="button"
            variant="outline"
            className="w-full justify-between text-sm text-gray-600 border-gray-200 hover:bg-gray-50 h-10"
          >
            <span>Test-Accounts</span>
            <ChevronDown className={`h-4 w-4 transition-transform ${showAccountSwitcher ? 'rotate-180' : ''}`} />
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="mt-3 animate-slide-in">
          <div className="p-3 bg-gray-50 rounded-xl border space-y-2">
            {/* First row: Berater + Mentor */}
            <div className="grid grid-cols-2 gap-2">
              {testAccounts.slice(0, 2).map((account) => {
                const IconComponent = account.icon;
                return (
                  <Button
                    key={account.role}
                    type="button"
                    variant="outline"
                    onClick={() => handleQuickLogin(account.role)}
                    className={`h-10 justify-center text-xs bg-gradient-to-r ${account.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`}
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                );
              })}
            </div>
            
            {/* Second row: Teamleiter + Manager */}
            <div className="grid grid-cols-2 gap-2">
              {testAccounts.slice(2, 4).map((account) => {
                const IconComponent = account.icon;
                return (
                  <Button
                    key={account.role}
                    type="button"
                    variant="outline"
                    onClick={() => handleQuickLogin(account.role)}
                    className={`h-10 justify-center text-xs bg-gradient-to-r ${account.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`}
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                );
              })}
            </div>
            
            {/* Third row: Admin centered */}
            <div className="flex justify-center">
              {testAccounts.slice(4, 5).map((account) => {
                const IconComponent = account.icon;
                return (
                  <Button
                    key={account.role}
                    type="button"
                    variant="outline"
                    onClick={() => handleQuickLogin(account.role)}
                    className={`h-10 w-24 justify-center text-xs bg-gradient-to-r ${account.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`}
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                );
              })}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Global error message */}
      {error && error.type !== 'validation' && (
        <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-xl">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <span className="text-sm font-medium text-red-700">{error.message}</span>
        </div>
      )}

      {/* Email/Username Input */}
      <FormField
        id="email"
        label="E-Mail oder Name"
        type="text"
        value={formState.emailOrName}
        onChange={handleEmailChange}
        onBlur={() => handleFieldBlur('emailOrName')}
        placeholder="<EMAIL> oder Name"
        error={validator.getFieldError('emailOrName') || (error?.field === 'emailOrName' ? error : null)}
        required
        icon={<User className="h-5 w-5" />}
        autoComplete="username"
        data-testid="login-email"
      />

      {/* Password Input */}
      <FormField
        id="password"
        label="Passwort"
        type="password"
        value={formState.password}
        onChange={handlePasswordChange}
        onBlur={() => handleFieldBlur('password')}
        placeholder="••••••••"
        error={validator.getFieldError('password') || (error?.field === 'password' ? error : null)}
        required
        icon={<Lock className="h-5 w-5" />}
        showPasswordToggle
        autoComplete="current-password"
        data-testid="login-password"
      />

      {/* Remember Me */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Switch
            id="remember-me"
            checked={rememberMe}
            onCheckedChange={setRememberMe}
            className="data-[state=checked]:bg-red-500"
          />
          <Label
            htmlFor="remember-me"
            className="text-sm font-medium text-gray-700 cursor-pointer"
          >
            Angemeldet bleiben
          </Label>
        </div>
        <span className="text-xs text-gray-500">
          {rememberMe ? '30 Tage' : '24 Stunden'}
        </span>
      </div>

      {/* Login Button */}
      <Button
        type="submit"
        className={cn(
          "w-full h-14 text-lg font-semibold rounded-xl transition-all duration-200 shadow-lg",
          "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800",
          "hover:scale-105 hover:shadow-xl touch-manipulation",
          "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
          "min-h-[44px]" // Ensure 44px minimum touch target
        )}
        disabled={
          isLoading ||
          formState.isSubmitting ||
          !formState.emailOrName.trim() ||
          !formState.password.trim() ||
          !validator.validateAll()
        }
        data-testid="login-submit"
      >
        {(isLoading || formState.isSubmitting) ? (
          <div className="flex items-center">
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
            Anmeldung...
          </div>
        ) : (
          'Anmelden'
        )}
      </Button>
    </form>
  );
};

export default LoginForm;
