
import { User, User<PERSON><PERSON> } from "../../types";

// Mock user data for demonstration
export const mockUsers: User[] = [
  // Test accounts with clear role differentiation
  {
    id: "1",
    name: "Test Berater",
    email: "<EMAIL>",
    role: "berater" as <PERSON><PERSON><PERSON><PERSON>,
    teamId: "team1",
    password: "test123",
  },
  {
    id: "2",
    name: "<PERSON> Mentor",
    email: "<EMAIL>",
    role: "mentor" as <PERSON><PERSON><PERSON><PERSON>,
    teamId: "team1",
    assignedBeraterIds: ["1", "5"],
    password: "test123",
  },
  {
    id: "3",
    name: "Test Teamleiter",
    email: "<EMAIL>",
    role: "teamleiter" as Use<PERSON><PERSON><PERSON>,
    teamId: "team1",
    password: "test123",
  },
  {
    id: "4",
    name: "Test Gebietsmanager",
    email: "<EMAIL>",
    role: "gebietsmanager" as User<PERSON><PERSON>,
    password: "test123",
  },
  {
    id: "5",
    name: "Test Admin",
    email: "<EMAIL>",
    role: "admin" as <PERSON><PERSON><PERSON><PERSON>,
    password: "test123",
  },
  {
    id: "6",
    name: "<PERSON><PERSON><PERSON> Berater",
    email: "<EMAIL>",
    role: "berater" as UserRole,
    teamId: "team1",
    mentorId: "2",
    password: "test123",
  },
  // Multi-role user with Admin + Berater privileges
  {
    id: "7",
    name: "Refik Emre Ak",
    email: "<EMAIL>",
    role: "admin" as UserRole, // Primary role for backward compatibility
    roles: ["admin", "berater"] as UserRole[], // Multiple roles
    teamId: "team1",
    password: "admin123",
    isMultiRole: true, // Flag to indicate multi-role capabilities
  }
];
