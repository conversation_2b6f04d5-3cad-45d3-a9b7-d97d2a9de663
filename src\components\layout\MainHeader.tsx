
import React from 'react';
import { User } from '@/types';
import { SidebarTrigger } from '@/components/ui/sidebar';

interface MainHeaderProps {
  title?: string;
  user: User | null;
  isMobile: boolean;
}

export const MainHeader: React.FC<MainHeaderProps> = ({ title, user, isMobile }) => {
  return (
    <header className="flex items-center justify-between h-16 md:h-20 bg-gradient-to-r from-white via-red-50/30 to-white border-b border-red-100 w-full shadow-lg backdrop-blur-sm transition-all duration-300">
      <div className="flex items-center px-4 md:px-6">
        <SidebarTrigger className="mr-3 md:mr-4 h-10 w-10 md:h-12 md:w-12 rounded-2xl bg-white/80 hover:bg-red-50 hover:text-red-600 border border-red-100 shadow-md hover:shadow-lg transition-all duration-200 touch-feedback" />
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-br from-red-500 to-red-600 p-2 rounded-xl shadow-lg">
            <span className="text-white font-bold text-lg">📋</span>
          </div>
          <h1 className="text-lg md:text-2xl font-bold bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent">
            {title || "Laufliste"}
          </h1>
        </div>
      </div>

      {user && !isMobile && (
        <div className="hidden md:flex items-center space-x-4 px-4 md:px-6">
          <div className="bg-gradient-to-r from-red-50 to-red-100 px-6 py-3 rounded-2xl border border-red-200 shadow-lg backdrop-blur-sm">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-md">
                <span className="text-white font-bold text-sm">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <span className="text-red-800 font-semibold block">
                  {user.name}
                </span>
                <span className="text-red-600 text-sm">
                  {user.role}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile user info - shown on small screens */}
      {user && isMobile && (
        <div className="flex md:hidden items-center px-4">
          <div className="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 rounded-xl border border-red-200 shadow-md">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xs">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <span className="text-sm font-semibold text-red-800">
                {user.name}
              </span>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};
