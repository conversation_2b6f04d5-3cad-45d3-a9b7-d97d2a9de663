
import {
  Address,
  House,
  Visit,
  Door,
  ProductEntry,
  VisitStatus,
  VisitPattern,
  VisitRecommendation,
  AddressVisitHistory,
} from "@/types";

export interface DataContextType {
  addresses: Address[];
  houses: House[];
  visits: Visit[];
  doors: Door[];
  products: ProductEntry[];
  visitPatterns: VisitPattern[];
  visitRecommendations: VisitRecommendation[];

  // Address operations
  addAddress: (address: Omit<Address, "id">) => Address;
  getAddressById: (id: string) => Address | undefined;

  // House operations
  addHouse: (house: Omit<House, "id" | "createdAt" | "createdBy">) => House;
  getHouseById: (id: string) => House | undefined;
  getHousesByAddress: (addressId: string) => House[];

  // Visit operations
  addVisit: (visit: Omit<Visit, "id" | "userId">) => Visit;
  getVisit: (id: string) => Visit | undefined;
  getVisitsByHouse: (houseId: string) => Visit[];
  getTodaysHouses: () => House[];
  getTodaysVisits: () => Visit[];
  updateVisitStatus: (visitId: string, status: VisitStatus, appointmentDate?: string, appointmentTime?: string, comment?: string) => void;

  // Door operations
  addDoor: (door: Omit<Door, "id">) => Door;
  getDoorsByVisit: (visitId: string) => Door[];
  updateDoorStatus: (doorId: string, status: VisitStatus, appointmentDate?: string, appointmentTime?: string, comment?: string) => void;

  // Product operations
  addProduct: (product: Omit<ProductEntry, "id" | "userId" | "timestamp">) => ProductEntry;
  getProductsByDoor: (doorId: string) => ProductEntry[];

  // Pattern analysis operations
  getAddressVisitHistory: (addressId: string) => AddressVisitHistory;
  getVisitRecommendations: (addressId: string) => VisitRecommendation[];
  getAddressesRequiringReturnVisits: () => { address: Address; recommendations: VisitRecommendation[]; failedVisitCount: number }[];
  updateVisitPatterns: (addressId: string) => void;
  getHighPriorityRecommendations: () => VisitRecommendation[];

  // Appointment operations
  getUpcomingAppointments: () => Array<{
    id: string;
    type: 'visit' | 'door';
    date: string;
    time: string;
    address: string;
    dateTime: Date;
  }>;
  getAppointmentsByDate: (date: string) => Array<{
    id: string;
    type: 'visit' | 'door';
    time: string;
    address: string;
  }>;
}
