
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { User } from '@/types';
import { 
  Sidebar, 
  SidebarContent, 
  SidebarMenu, 
  SidebarMenuItem, 
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarFooter,
  SidebarHeader
} from '@/components/ui/sidebar';
import {
  LayoutPanelLeft,
  Home,
  Map,
  BarChart4,
  User as UserIcon,
  LogOut,
  Package,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { SidebarNavigation } from './SidebarNavigation';
import { SidebarCalendar } from '@/components/calendar/SidebarCalendar';

interface MainSidebarProps {
  user: User | null;
  logout: () => Promise<void>;
}

export const MainSidebar: React.FC<MainSidebarProps> = ({ user, logout }) => {
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  return (
    <Sidebar className="border-r border-border transition-colors duration-300">
      {/* Header with branding */}
      <SidebarHeader className="p-4 md:p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="bg-red-100 p-2 rounded-xl">
            <Package className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-foreground">Visit Flow</h2>
            <p className="text-sm text-muted-foreground">Compass</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2 md:px-4">
        <SidebarGroup>
          <SidebarGroupLabel className="text-muted-foreground font-semibold px-2 py-2 text-sm">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              <SidebarMenuItem>
                <SidebarMenuButton 
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback group"
                >
                  <a href="/" className="flex items-center gap-3 px-3">
                    <Home className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Laufliste</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
              
              <SidebarMenuItem>
                <SidebarMenuButton 
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 touch-feedback group"
                >
                  <a href="/map" className="flex items-center gap-3 px-3">
                    <Map className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Kartenansicht</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton 
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback group"
                >
                  <a href="/statistics" className="flex items-center gap-3 px-3">
                    <BarChart4 className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Statistiken</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton 
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 touch-feedback group"
                >
                  <a href="/daily-view" className="flex items-center gap-3 px-3">
                    <LayoutPanelLeft className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Tagesübersicht</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 touch-feedback group"
                >
                  <a href="/calendar" className="flex items-center gap-3 px-3">
                    <Calendar className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Termine</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 touch-feedback group"
                >
                  <a href="/pattern-analysis-demo" className="flex items-center gap-3 px-3">
                    <TrendingUp className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Smart Visit Assistant</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  className="h-12 md:h-14 rounded-xl hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 touch-feedback group"
                >
                  <a href="/profile" className="flex items-center gap-3 px-3">
                    <UserIcon className="h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span className="font-medium">Profil</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        
        {/* Calendar Widget */}
        <SidebarGroup className="mt-6">
          <SidebarGroupContent>
            <SidebarCalendar />
          </SidebarGroupContent>
        </SidebarGroup>
        
        {/* Role-specific navigation */}
        {user && <SidebarNavigation user={user} />}
      </SidebarContent>
      
      {/* User info and logout at the bottom */}
      {user && (
        <SidebarFooter className="p-4 border-t border-border">
          {/* User info */}
          <div className="mb-3 p-3 bg-muted rounded-xl">
            <p className="text-sm font-medium text-foreground truncate">
              {user.name}
            </p>
            <p className="text-xs text-muted-foreground">
              {user.role}
            </p>
          </div>
          
          {/* Logout button */}
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton 
                onClick={handleLogout} 
                className="w-full h-12 md:h-14 rounded-xl bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-all duration-200 touch-feedback"
              >
                <LogOut className="h-5 w-5" />
                <span className="font-medium">Logout</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      )}
    </Sidebar>
  );
};
