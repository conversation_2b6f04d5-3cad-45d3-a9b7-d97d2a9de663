
import React, { useState, useRef, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Check, LucideIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface ImprovedAutocompleteProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  suggestions: string[];
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  maxSuggestions?: number;
  icon?: LucideIcon;
  isValid?: boolean;
  debounceMs?: number;
}

const ImprovedAutocomplete: React.FC<ImprovedAutocompleteProps> = ({
  id,
  label,
  value,
  onChange,
  suggestions,
  placeholder,
  required = false,
  className,
  disabled = false,
  maxSuggestions = 10,
  icon: Icon,
  isValid = false,
  debounceMs = 150,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [debouncedValue, setDebouncedValue] = useState(value);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounce the input value
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [value, debounceMs]);

  // Filter suggestions based on debounced value
  const filteredSuggestions = suggestions
    .filter(suggestion => 
      suggestion.toLowerCase().includes(debouncedValue.toLowerCase())
    )
    .slice(0, maxSuggestions);

  // Prioritize exact matches and frequently used items
  const sortedSuggestions = filteredSuggestions.sort((a, b) => {
    const aExact = a.toLowerCase().startsWith(debouncedValue.toLowerCase());
    const bExact = b.toLowerCase().startsWith(debouncedValue.toLowerCase());
    if (aExact && !bExact) return -1;
    if (!aExact && bExact) return 1;
    return 0;
  });

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => 
            prev < sortedSuggestions.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => (prev > 0 ? prev - 1 : 0));
          break;
        case 'Enter':
          if (highlightedIndex >= 0 && highlightedIndex < sortedSuggestions.length) {
            e.preventDefault();
            handleSuggestionSelect(sortedSuggestions[highlightedIndex]);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setHighlightedIndex(-1);
          break;
        case 'Tab':
          if (highlightedIndex >= 0 && highlightedIndex < sortedSuggestions.length) {
            e.preventDefault();
            handleSuggestionSelect(sortedSuggestions[highlightedIndex]);
          }
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, highlightedIndex, sortedSuggestions]);

  // Scroll to highlighted item
  useEffect(() => {
    if (isOpen && highlightedIndex >= 0 && suggestionsRef.current) {
      const highlightedElement = suggestionsRef.current.children[highlightedIndex] as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth',
        });
      }
    }
  }, [highlightedIndex, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    const hasExactMatch = suggestions.some(suggestion => 
      suggestion.toLowerCase() === newValue.toLowerCase()
    );
    
    setIsOpen(!hasExactMatch && newValue.length > 0);
    setHighlightedIndex(-1);
  };

  const handleSuggestionSelect = (suggestion: string) => {
    onChange(suggestion);
    setIsOpen(false);
    setHighlightedIndex(-1);
    inputRef.current?.focus();
  };

  // Use onMouseDown instead of onClick for immediate response
  const handleSuggestionMouseDown = (e: React.MouseEvent, suggestion: string) => {
    e.preventDefault(); // Prevent input blur
    handleSuggestionSelect(suggestion);
  };

  return (
    <div className="mobile-form-group" ref={wrapperRef}>
      <Label htmlFor={id} className="mobile-label flex items-center gap-2">
        {Icon && <Icon className="h-4 w-4 text-red-500" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <div className="relative">
        <Input
          id={id}
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onFocus={() => {
            const hasExactMatch = suggestions.some(suggestion =>
              suggestion.toLowerCase() === value.toLowerCase()
            );
            if (!hasExactMatch && value.length > 0) {
              setIsOpen(true);
            }
          }}
          placeholder={placeholder}
          required={required}
          className={`mobile-input bg-white/90 backdrop-blur-sm border-2 rounded-xl transition-all duration-200 touch-feedback ${
            isValid
              ? 'border-green-400 focus:border-green-500 focus:ring-green-500'
              : 'border-gray-200 focus:border-red-500 focus:ring-red-500'
          } ${className}`}
          disabled={disabled}
          style={{ fontSize: '16px' }} // Prevents zoom on iOS
        />
        {isValid && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <Check className="h-5 w-5 text-green-500" />
          </div>
        )}
        
        {isOpen && sortedSuggestions.length > 0 && (
          <div
            ref={suggestionsRef}
            className="absolute z-50 w-full mt-2 bg-white/95 backdrop-blur-sm border-2 border-gray-200 rounded-xl shadow-2xl max-h-80 overflow-auto animate-fade-in"
          >
            {sortedSuggestions.map((suggestion, index) => (
              <Button
                key={index}
                type="button"
                variant="ghost"
                className={`mobile-list-item w-full justify-start text-left rounded-none first:rounded-t-xl last:rounded-b-xl touch-feedback ${
                  index === highlightedIndex
                    ? 'bg-red-50 text-red-600 font-semibold'
                    : 'hover:bg-red-50 hover:text-red-600'
                }`}
                onMouseDown={(e) => handleSuggestionMouseDown(e, suggestion)}
                onMouseEnter={() => setHighlightedIndex(index)}
                style={{ minHeight: '48px' }} // Ensure touch target
              >
                <span className="truncate text-base">{suggestion}</span>
              </Button>
            ))}
          </div>
        )}
        
        {isOpen && value && sortedSuggestions.length === 0 && (
          <div className="absolute z-50 w-full mt-2 bg-white/95 backdrop-blur-sm border-2 border-gray-200 rounded-xl shadow-lg p-4 text-center text-gray-500 animate-fade-in">
            <p className="text-lg">Keine Vorschläge gefunden</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImprovedAutocomplete;
