import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { validateAllTouchTargets, TOUCH_TARGET_SIZES } from '@/utils/mobileOptimization';
import { AlertTriangle, CheckCircle, RefreshCw, Eye, EyeOff } from 'lucide-react';

interface TouchTargetViolation {
  element: HTMLElement;
  currentSize: { width: number; height: number };
  tagName: string;
  className: string;
  id: string;
  textContent: string;
  selector: string;
}

export const TouchTargetValidator: React.FC = () => {
  const [violations, setViolations] = useState<TouchTargetViolation[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [showHighlights, setShowHighlights] = useState(false);
  const [lastValidation, setLastValidation] = useState<Date | null>(null);

  const runValidation = () => {
    setIsValidating(true);
    
    // Small delay to show loading state
    setTimeout(() => {
      const foundViolations = validateAllTouchTargets();
      setViolations(foundViolations);
      setLastValidation(new Date());
      setIsValidating(false);
    }, 500);
  };

  const highlightViolations = () => {
    // Remove existing highlights
    document.querySelectorAll('.touch-target-violation-highlight').forEach(el => {
      el.classList.remove('touch-target-violation-highlight');
    });

    if (showHighlights) {
      violations.forEach(violation => {
        violation.element.classList.add('touch-target-violation-highlight');
      });
    }
  };

  const fixViolation = (violation: TouchTargetViolation) => {
    const element = violation.element;
    
    // Apply appropriate mobile classes based on element type
    if (element.tagName === 'BUTTON') {
      element.classList.add('mobile-button', 'touch-feedback');
      if (element.getAttribute('data-primary') === 'true') {
        element.classList.add('mobile-action-button');
      }
    } else if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
      element.classList.add('mobile-input', 'touch-feedback');
    } else if (element.getAttribute('role') === 'tab') {
      element.classList.add('mobile-tab', 'touch-feedback');
    } else {
      // Generic fix - ensure minimum size
      element.style.minHeight = `${TOUCH_TARGET_SIZES.MINIMUM}px`;
      element.style.minWidth = `${TOUCH_TARGET_SIZES.MINIMUM}px`;
      element.classList.add('touch-feedback');
    }

    // Re-run validation
    setTimeout(runValidation, 100);
  };

  const autoFixAll = () => {
    violations.forEach(violation => {
      fixViolation(violation);
    });
  };

  useEffect(() => {
    // Run initial validation
    runValidation();
  }, []);

  useEffect(() => {
    highlightViolations();
  }, [showHighlights, violations]);

  const getSeverityColor = (width: number, height: number) => {
    const minSize = Math.min(width, height);
    if (minSize < 32) return 'bg-red-100 text-red-800 border-red-200';
    if (minSize < 40) return 'bg-orange-100 text-orange-800 border-orange-200';
    return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  };

  return (
    <>
      {/* CSS for highlighting violations */}
      <style>{`
        .touch-target-violation-highlight {
          outline: 3px solid #ef4444 !important;
          outline-offset: 2px !important;
          background-color: rgba(239, 68, 68, 0.1) !important;
          position: relative !important;
        }
        .touch-target-violation-highlight::after {
          content: '⚠️';
          position: absolute;
          top: -10px;
          right: -10px;
          background: #ef4444;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          z-index: 1000;
        }
      `}</style>

      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {violations.length === 0 ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              )}
              Touch Target Validator
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHighlights(!showHighlights)}
                className="flex items-center gap-2"
              >
                {showHighlights ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showHighlights ? 'Hide' : 'Show'} Highlights
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={runValidation}
                disabled={isValidating}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isValidating ? 'animate-spin' : ''}`} />
                Validate
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="space-y-4">
            {/* Summary */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">
                  {violations.length === 0 ? (
                    <span className="text-green-600 font-medium">✅ All touch targets meet the 44px minimum requirement</span>
                  ) : (
                    <span className="text-red-600 font-medium">⚠️ Found {violations.length} touch target violations</span>
                  )}
                </p>
                {lastValidation && (
                  <p className="text-xs text-gray-500 mt-1">
                    Last validated: {lastValidation.toLocaleTimeString()}
                  </p>
                )}
              </div>
              {violations.length > 0 && (
                <Button onClick={autoFixAll} className="bg-red-600 hover:bg-red-700 text-white">
                  Auto-Fix All
                </Button>
              )}
            </div>

            {/* Violations List */}
            {violations.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-semibold text-lg">Touch Target Violations:</h3>
                {violations.map((violation, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={getSeverityColor(violation.currentSize.width, violation.currentSize.height)}>
                            {violation.currentSize.width.toFixed(0)}×{violation.currentSize.height.toFixed(0)}px
                          </Badge>
                          <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                            {violation.tagName.toLowerCase()}
                          </code>
                          {violation.className && (
                            <code className="text-xs bg-blue-100 px-2 py-1 rounded text-blue-800">
                              .{violation.className.split(' ').slice(0, 2).join('.')}
                            </code>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-1">
                          <strong>Selector:</strong> {violation.selector}
                        </p>
                        {violation.textContent && (
                          <p className="text-sm text-gray-600">
                            <strong>Content:</strong> "{violation.textContent}"
                          </p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        onClick={() => fixViolation(violation)}
                        className="ml-4"
                      >
                        Fix
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Guidelines */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Touch Target Guidelines:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Minimum:</strong> 44×44px (Apple/Google standard)</li>
                <li>• <strong>Comfortable:</strong> 48×48px (recommended for primary actions)</li>
                <li>• <strong>Large:</strong> 56×56px (critical actions)</li>
                <li>• Use <code>.mobile-button</code>, <code>.mobile-input</code>, <code>.mobile-action-button</code> classes</li>
                <li>• Add <code>data-primary="true"</code> for important buttons</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default TouchTargetValidator;
