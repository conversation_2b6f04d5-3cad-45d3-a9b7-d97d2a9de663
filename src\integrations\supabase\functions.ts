
import { supabase } from './client';

// Mock data for areas when Supabase is not available
const mockAreas = [
  {
    id: 'area-1',
    name: 'Hamburg Nord',
    description: 'Nördliche Stadtteile von Hamburg',
    postal_codes: ['20095', '20099', '20144', '20146'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'area-2',
    name: 'München Süd',
    description: 'Südliche Stadtteile von München',
    postal_codes: ['80331', '80333', '80335', '80337'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Check if Supabase is available and user is authenticated
const isSupabaseAvailable = async (): Promise<boolean> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      // Try to authenticate with a service key for admin operations
      // In production, this should be handled server-side
      console.log('No Supabase session found, using mock data');
      return false;
    }
    return true;
  } catch (error) {
    console.log('Supabase not available, using mock data:', error);
    return false;
  }
};

// Areas Management
export const createArea = async (areaData: {
  name: string;
  description?: string;
  postal_codes?: string[];
}) => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    // Use mock data
    const newArea = {
      id: `area-${Date.now()}`,
      name: areaData.name,
      description: areaData.description,
      postal_codes: areaData.postal_codes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Store in localStorage for persistence
    const existingAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const updatedAreas = [...existingAreas, newArea];
    localStorage.setItem('mock_areas', JSON.stringify(updatedAreas));

    return newArea;
  }

  try {
    const { data, error } = await supabase
      .from('areas')
      .insert([areaData])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    // Fallback to mock implementation
    const newArea = {
      id: `area-${Date.now()}`,
      name: areaData.name,
      description: areaData.description,
      postal_codes: areaData.postal_codes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const existingAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const updatedAreas = [...existingAreas, newArea];
    localStorage.setItem('mock_areas', JSON.stringify(updatedAreas));

    return newArea;
  }
};

export const getAreas = async () => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    // Return mock data combined with localStorage data
    const storedAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const allAreas = [...mockAreas, ...storedAreas];
    return allAreas.sort((a, b) => a.name.localeCompare(b.name));
  }

  try {
    const { data, error } = await supabase
      .from('areas')
      .select('*')
      .order('name');

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    // Fallback to mock data
    const storedAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const allAreas = [...mockAreas, ...storedAreas];
    return allAreas.sort((a, b) => a.name.localeCompare(b.name));
  }
};

export const updateArea = async (id: string, updates: {
  name?: string;
  description?: string;
  postal_codes?: string[];
}) => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    // Update mock data
    const storedAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const allAreas = [...mockAreas, ...storedAreas];
    const areaIndex = allAreas.findIndex(area => area.id === id);

    if (areaIndex === -1) {
      throw new Error('Area not found');
    }

    const updatedArea = {
      ...allAreas[areaIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };

    // Update in localStorage if it's a stored area
    const storedIndex = storedAreas.findIndex(area => area.id === id);
    if (storedIndex !== -1) {
      storedAreas[storedIndex] = updatedArea;
      localStorage.setItem('mock_areas', JSON.stringify(storedAreas));
    }

    return updatedArea;
  }

  try {
    const { data, error } = await supabase
      .from('areas')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    // Fallback to mock implementation
    const storedAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const allAreas = [...mockAreas, ...storedAreas];
    const areaIndex = allAreas.findIndex(area => area.id === id);

    if (areaIndex === -1) {
      throw new Error('Area not found');
    }

    const updatedArea = {
      ...allAreas[areaIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };

    const storedIndex = storedAreas.findIndex(area => area.id === id);
    if (storedIndex !== -1) {
      storedAreas[storedIndex] = updatedArea;
      localStorage.setItem('mock_areas', JSON.stringify(storedAreas));
    }

    return updatedArea;
  }
};

export const deleteArea = async (id: string) => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    // Delete from mock data
    const storedAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const filteredAreas = storedAreas.filter(area => area.id !== id);
    localStorage.setItem('mock_areas', JSON.stringify(filteredAreas));
    return;
  }

  try {
    const { error } = await supabase
      .from('areas')
      .delete()
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    // Fallback to mock implementation
    const storedAreas = JSON.parse(localStorage.getItem('mock_areas') || '[]');
    const filteredAreas = storedAreas.filter(area => area.id !== id);
    localStorage.setItem('mock_areas', JSON.stringify(filteredAreas));
  }
};

// Mock data for teams
const mockTeams = [
  {
    id: 'team-1',
    name: 'Nord-Team',
    description: 'Verantwortlich für nördliche Gebiete',
    area_id: 'area-1',
    team_leader_id: 'leader-1',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'team-2',
    name: 'Süd-Team',
    description: 'Verantwortlich für südliche Gebiete',
    area_id: 'area-2',
    team_leader_id: 'leader-2',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Teams Management
export const createTeam = async (teamData: {
  name: string;
  description?: string;
  area_id?: string;
  team_leader_id?: string;
}) => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    const newTeam = {
      id: `team-${Date.now()}`,
      name: teamData.name,
      description: teamData.description,
      area_id: teamData.area_id,
      team_leader_id: teamData.team_leader_id,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const existingTeams = JSON.parse(localStorage.getItem('mock_teams') || '[]');
    const updatedTeams = [...existingTeams, newTeam];
    localStorage.setItem('mock_teams', JSON.stringify(updatedTeams));

    return newTeam;
  }

  try {
    const { data, error } = await supabase
      .from('teams')
      .insert([teamData])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    const newTeam = {
      id: `team-${Date.now()}`,
      name: teamData.name,
      description: teamData.description,
      area_id: teamData.area_id,
      team_leader_id: teamData.team_leader_id,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const existingTeams = JSON.parse(localStorage.getItem('mock_teams') || '[]');
    const updatedTeams = [...existingTeams, newTeam];
    localStorage.setItem('mock_teams', JSON.stringify(updatedTeams));

    return newTeam;
  }
};

export const getTeams = async () => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    const storedTeams = JSON.parse(localStorage.getItem('mock_teams') || '[]');
    const allTeams = [...mockTeams, ...storedTeams];
    return allTeams.sort((a, b) => a.name.localeCompare(b.name));
  }

  try {
    const { data, error } = await supabase
      .from('teams')
      .select(`
        *,
        areas (
          id,
          name
        )
      `)
      .order('name');

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    const storedTeams = JSON.parse(localStorage.getItem('mock_teams') || '[]');
    const allTeams = [...mockTeams, ...storedTeams];
    return allTeams.sort((a, b) => a.name.localeCompare(b.name));
  }
};

export const updateTeam = async (id: string, updates: {
  name?: string;
  description?: string;
  area_id?: string;
  team_leader_id?: string;
  is_active?: boolean;
}) => {
  const { data, error } = await supabase
    .from('teams')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const deleteTeam = async (id: string) => {
  const { error } = await supabase
    .from('teams')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
};

// Mock user profiles
const mockUserProfiles = [
  {
    id: 'profile-1',
    user_id: 'admin-1',
    full_name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    teams: { id: 'team-1', name: 'Admin Team' }
  },
  {
    id: 'profile-2',
    user_id: 'berater-1',
    full_name: 'Test Berater',
    email: '<EMAIL>',
    role: 'berater',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    teams: { id: 'team-1', name: 'Nord-Team' }
  }
];

// User Profiles Management
export const getUserProfiles = async () => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    const storedProfiles = JSON.parse(localStorage.getItem('mock_user_profiles') || '[]');
    const allProfiles = [...mockUserProfiles, ...storedProfiles];
    return allProfiles.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        teams (
          id,
          name
        )
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    const storedProfiles = JSON.parse(localStorage.getItem('mock_user_profiles') || '[]');
    const allProfiles = [...mockUserProfiles, ...storedProfiles];
    return allProfiles.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }
};

export const updateUserProfile = async (id: string, updates: {
  team_id?: string;
  is_active?: boolean;
  total_visits?: number;
  total_sales?: number;
}) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const resetUserStatistics = async (userId: string, adminId: string) => {
  const { data, error } = await supabase.rpc('reset_user_statistics', {
    p_user_id: userId,
    p_admin_id: adminId
  });
  
  if (error) throw error;
  return data;
};

// Mock audit logs
const mockAuditLogs = [
  {
    id: 'audit-1',
    admin_user_id: 'admin-1',
    action_type: 'role_assignment',
    target_type: 'user',
    target_id: 'user-1',
    description: 'Bulk role assignment: mentor to 3 users',
    created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    ip_address: '***********',
    user_agent: 'Mozilla/5.0...'
  },
  {
    id: 'audit-2',
    admin_user_id: 'admin-1',
    action_type: 'team_assignment',
    target_type: 'team',
    target_id: 'team-1',
    description: 'Team member assignment',
    created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    ip_address: '***********',
    user_agent: 'Mozilla/5.0...'
  }
];

// Audit Logs
export const getAuditLogs = async (limit: number = 50) => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    const storedLogs = JSON.parse(localStorage.getItem('mock_audit_logs') || '[]');
    const allLogs = [...mockAuditLogs, ...storedLogs];
    return allLogs
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, limit);
  }

  try {
    const { data, error } = await supabase
      .from('audit_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    const storedLogs = JSON.parse(localStorage.getItem('mock_audit_logs') || '[]');
    const allLogs = [...mockAuditLogs, ...storedLogs];
    return allLogs
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, limit);
  }
};

export const logAdminAction = async (actionData: {
  admin_user_id: string;
  action_type: string;
  target_type: string;
  target_id?: string;
  old_data?: any;
  new_data?: any;
  description?: string;
}) => {
  const isAvailable = await isSupabaseAvailable();

  if (!isAvailable) {
    const newLog = {
      id: `audit-${Date.now()}`,
      admin_user_id: actionData.admin_user_id,
      action_type: actionData.action_type,
      target_type: actionData.target_type,
      target_id: actionData.target_id,
      old_data: actionData.old_data,
      new_data: actionData.new_data,
      description: actionData.description,
      created_at: new Date().toISOString(),
      ip_address: '127.0.0.1',
      user_agent: navigator.userAgent
    };

    const existingLogs = JSON.parse(localStorage.getItem('mock_audit_logs') || '[]');
    const updatedLogs = [newLog, ...existingLogs];
    localStorage.setItem('mock_audit_logs', JSON.stringify(updatedLogs));

    return newLog;
  }

  try {
    const { data, error } = await supabase.rpc('log_admin_action', {
      p_admin_user_id: actionData.admin_user_id,
      p_action_type: actionData.action_type,
      p_target_type: actionData.target_type,
      p_target_id: actionData.target_id,
      p_old_data: actionData.old_data,
      p_new_data: actionData.new_data,
      p_description: actionData.description
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Supabase error, falling back to mock:', error);
    const newLog = {
      id: `audit-${Date.now()}`,
      admin_user_id: actionData.admin_user_id,
      action_type: actionData.action_type,
      target_type: actionData.target_type,
      target_id: actionData.target_id,
      old_data: actionData.old_data,
      new_data: actionData.new_data,
      description: actionData.description,
      created_at: new Date().toISOString(),
      ip_address: '127.0.0.1',
      user_agent: navigator.userAgent
    };

    const existingLogs = JSON.parse(localStorage.getItem('mock_audit_logs') || '[]');
    const updatedLogs = [newLog, ...existingLogs];
    localStorage.setItem('mock_audit_logs', JSON.stringify(updatedLogs));

    return newLog;
  }
};
