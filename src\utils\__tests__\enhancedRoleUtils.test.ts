import { describe, it, expect } from 'vitest';
import {
  getUserPermissions,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  validateBulkRoleAssignment,
  canPerformAdminAction,
  ROLE_PERMISSIONS
} from '../roleUtils';
import { User, BulkRoleAssignment } from '@/types';

describe('Enhanced Role Utilities', () => {
  const mockUsers: Record<string, User> = {
    berater: {
      id: 'berater-1',
      name: 'Test Berater',
      email: '<EMAIL>',
      role: 'berater',
      isActive: true
    },
    mentor: {
      id: 'mentor-1',
      name: 'Test Mentor',
      email: '<EMAIL>',
      role: 'mentor',
      isActive: true
    },
    teamleiter: {
      id: 'teamleiter-1',
      name: 'Test Teamleiter',
      email: '<EMAIL>',
      role: 'teamleiter',
      isActive: true
    },
    gebietsmanager: {
      id: 'manager-1',
      name: 'Test Manager',
      email: '<EMAIL>',
      role: 'gebietsmanager',
      isActive: true
    },
    admin: {
      id: 'admin-1',
      name: 'Test Admin',
      email: '<EMAIL>',
      role: 'admin',
      isActive: true
    },
    multiRole: {
      id: 'multi-1',
      name: 'Multi Role User',
      email: '<EMAIL>',
      role: 'teamleiter',
      roles: ['teamleiter', 'mentor'],
      isMultiRole: true,
      isActive: true
    }
  };

  describe('ROLE_PERMISSIONS', () => {
    it('defines permissions for all roles', () => {
      expect(ROLE_PERMISSIONS.berater).toBeDefined();
      expect(ROLE_PERMISSIONS.mentor).toBeDefined();
      expect(ROLE_PERMISSIONS.teamleiter).toBeDefined();
      expect(ROLE_PERMISSIONS.gebietsmanager).toBeDefined();
      expect(ROLE_PERMISSIONS.admin).toBeDefined();
    });

    it('admin has all permissions', () => {
      const adminPermissions = ROLE_PERMISSIONS.admin;
      expect(adminPermissions).toContain('user.read');
      expect(adminPermissions).toContain('user.write');
      expect(adminPermissions).toContain('user.delete');
      expect(adminPermissions).toContain('user.bulk_assign');
      expect(adminPermissions).toContain('system.config');
      expect(adminPermissions).toContain('data.backup');
    });

    it('berater has limited permissions', () => {
      const beraterPermissions = ROLE_PERMISSIONS.berater;
      expect(beraterPermissions).toContain('user.read');
      expect(beraterPermissions).toContain('data.export');
      expect(beraterPermissions).not.toContain('user.write');
      expect(beraterPermissions).not.toContain('system.config');
    });
  });

  describe('getUserPermissions', () => {
    it('returns correct permissions for single role user', () => {
      const permissions = getUserPermissions(mockUsers.berater);
      expect(permissions).toEqual(ROLE_PERMISSIONS.berater);
    });

    it('returns combined permissions for multi-role user', () => {
      const permissions = getUserPermissions(mockUsers.multiRole);
      const expectedPermissions = [
        ...ROLE_PERMISSIONS.teamleiter,
        ...ROLE_PERMISSIONS.mentor
      ];
      
      // Remove duplicates
      const uniquePermissions = [...new Set(expectedPermissions)];
      expect(permissions.sort()).toEqual(uniquePermissions.sort());
    });

    it('returns admin permissions for admin user', () => {
      const permissions = getUserPermissions(mockUsers.admin);
      expect(permissions).toEqual(ROLE_PERMISSIONS.admin);
      expect(permissions).toContain('system.config');
      expect(permissions).toContain('user.bulk_assign');
    });
  });

  describe('hasPermission', () => {
    it('returns true when user has the permission', () => {
      expect(hasPermission(mockUsers.admin, 'user.write')).toBe(true);
      expect(hasPermission(mockUsers.berater, 'user.read')).toBe(true);
      expect(hasPermission(mockUsers.teamleiter, 'team.write')).toBe(true);
    });

    it('returns false when user does not have the permission', () => {
      expect(hasPermission(mockUsers.berater, 'user.write')).toBe(false);
      expect(hasPermission(mockUsers.mentor, 'system.config')).toBe(false);
      expect(hasPermission(mockUsers.teamleiter, 'user.delete')).toBe(false);
    });

    it('works correctly for multi-role users', () => {
      expect(hasPermission(mockUsers.multiRole, 'team.write')).toBe(true); // from teamleiter
      expect(hasPermission(mockUsers.multiRole, 'user.write')).toBe(true); // from mentor
      expect(hasPermission(mockUsers.multiRole, 'system.config')).toBe(false); // neither role has this
    });
  });

  describe('hasAnyPermission', () => {
    it('returns true when user has at least one permission', () => {
      expect(hasAnyPermission(mockUsers.berater, ['user.read', 'user.write'])).toBe(true);
      expect(hasAnyPermission(mockUsers.admin, ['system.config', 'nonexistent.permission'])).toBe(true);
    });

    it('returns false when user has none of the permissions', () => {
      expect(hasAnyPermission(mockUsers.berater, ['user.write', 'system.config'])).toBe(false);
      expect(hasAnyPermission(mockUsers.mentor, ['system.config', 'data.backup'])).toBe(false);
    });
  });

  describe('hasAllPermissions', () => {
    it('returns true when user has all permissions', () => {
      expect(hasAllPermissions(mockUsers.admin, ['user.read', 'user.write'])).toBe(true);
      expect(hasAllPermissions(mockUsers.berater, ['user.read', 'data.export'])).toBe(true);
    });

    it('returns false when user is missing any permission', () => {
      expect(hasAllPermissions(mockUsers.berater, ['user.read', 'user.write'])).toBe(false);
      expect(hasAllPermissions(mockUsers.mentor, ['user.read', 'system.config'])).toBe(false);
    });
  });

  describe('validateBulkRoleAssignment', () => {
    const validAssignment: BulkRoleAssignment = {
      userIds: ['user-1', 'user-2'],
      targetRole: 'mentor',
      reason: 'Test assignment'
    };

    it('validates successful assignment for admin', () => {
      const result = validateBulkRoleAssignment(mockUsers.admin, validAssignment);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects assignment for user without bulk_assign permission', () => {
      const result = validateBulkRoleAssignment(mockUsers.berater, validAssignment);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Sie haben keine Berechtigung für Massenzuweisungen');
    });

    it('rejects assignment when no users selected', () => {
      const invalidAssignment = { ...validAssignment, userIds: [] };
      const result = validateBulkRoleAssignment(mockUsers.admin, invalidAssignment);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Mindestens ein Benutzer muss ausgewählt werden');
    });

    it('rejects assignment with past expiration date', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      const invalidAssignment = { 
        ...validAssignment, 
        expiresAt: pastDate.toISOString() 
      };
      
      const result = validateBulkRoleAssignment(mockUsers.admin, invalidAssignment);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Das Ablaufdatum muss in der Zukunft liegen');
    });

    it('accepts assignment with future expiration date', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      const validAssignmentWithExpiry = { 
        ...validAssignment, 
        expiresAt: futureDate.toISOString() 
      };
      
      const result = validateBulkRoleAssignment(mockUsers.admin, validAssignmentWithExpiry);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('canPerformAdminAction', () => {
    it('allows admin to perform all actions', () => {
      expect(canPerformAdminAction(mockUsers.admin, 'role_assignment')).toBe(true);
      expect(canPerformAdminAction(mockUsers.admin, 'system_config')).toBe(true);
      expect(canPerformAdminAction(mockUsers.admin, 'data_backup')).toBe(true);
    });

    it('allows teamleiter to assign team members', () => {
      expect(canPerformAdminAction(mockUsers.teamleiter, 'team_assignment')).toBe(true);
    });

    it('prevents berater from performing admin actions', () => {
      expect(canPerformAdminAction(mockUsers.berater, 'role_assignment')).toBe(false);
      expect(canPerformAdminAction(mockUsers.berater, 'system_config')).toBe(false);
      expect(canPerformAdminAction(mockUsers.berater, 'data_backup')).toBe(false);
    });

    it('allows users with appropriate permissions to perform specific actions', () => {
      expect(canPerformAdminAction(mockUsers.gebietsmanager, 'data_export')).toBe(true);
      expect(canPerformAdminAction(mockUsers.gebietsmanager, 'data_import')).toBe(true);
      expect(canPerformAdminAction(mockUsers.mentor, 'user_activation')).toBe(true);
    });

    it('returns false for unknown action types', () => {
      expect(canPerformAdminAction(mockUsers.admin, 'unknown_action')).toBe(false);
    });

    it('works correctly for multi-role users', () => {
      // Multi-role user has both teamleiter and mentor permissions
      expect(canPerformAdminAction(mockUsers.multiRole, 'team_assignment')).toBe(true); // from teamleiter
      expect(canPerformAdminAction(mockUsers.multiRole, 'user_activation')).toBe(true); // from mentor
      expect(canPerformAdminAction(mockUsers.multiRole, 'system_config')).toBe(false); // neither role has this
    });
  });

  describe('Permission Hierarchy', () => {
    it('ensures higher roles have more permissions than lower roles', () => {
      const beraterPerms = ROLE_PERMISSIONS.berater;
      const mentorPerms = ROLE_PERMISSIONS.mentor;
      const teamleiterPerms = ROLE_PERMISSIONS.teamleiter;
      const managerPerms = ROLE_PERMISSIONS.gebietsmanager;
      const adminPerms = ROLE_PERMISSIONS.admin;

      expect(mentorPerms.length).toBeGreaterThan(beraterPerms.length);
      expect(teamleiterPerms.length).toBeGreaterThan(mentorPerms.length);
      expect(managerPerms.length).toBeGreaterThan(teamleiterPerms.length);
      expect(adminPerms.length).toBeGreaterThan(managerPerms.length);
    });

    it('ensures all berater permissions are included in mentor permissions', () => {
      const beraterPerms = ROLE_PERMISSIONS.berater;
      const mentorPerms = ROLE_PERMISSIONS.mentor;
      
      beraterPerms.forEach(permission => {
        expect(mentorPerms).toContain(permission);
      });
    });
  });
});
