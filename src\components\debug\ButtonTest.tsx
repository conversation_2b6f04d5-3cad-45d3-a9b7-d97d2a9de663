import React from 'react';
import { StatusButton } from '@/design-system/components/Button';
import { StatusButtons } from '@/components/visit/StatusButtons';
import { SimpleStatusButtons } from '@/components/visit/SimpleStatusButtons';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { VisitStatus } from '@/types';

const ButtonTest: React.FC = () => {
  const handleStatusUpdate = (status: VisitStatus, comment?: string) => {
    console.log('Status clicked:', status, 'Comment:', comment);
    alert(`Status clicked: ${status}${comment ? `\nKommentar: ${comment}` : ''}`);
  };

  const statusOptions: VisitStatus[] = [
    'Angetroffen → Sale',
    'Angetroffen → Termin',
    'Angetroffen → Kein Interesse',
    'N/A'
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Button Test - Debug</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Status Buttons Test</h3>
            
            {statusOptions.map((status) => (
              <div key={status} className="border p-2 rounded">
                <p className="text-sm text-gray-600 mb-2">Status: {status}</p>
                <StatusButton
                  status={status}
                  onClick={() => handleStatusUpdate(status)}
                  loading={false}
                  disabled={false}
                />
              </div>
            ))}
            
            <div className="mt-8">
              <h4 className="text-md font-semibold mb-2">Simple StatusButtons Test</h4>
              <SimpleStatusButtons
                currentStatus="N/A"
                onStatusUpdate={handleStatusUpdate}
                isUpdating={false}
              />
            </div>

            <div className="mt-8">
              <h4 className="text-md font-semibold mb-2">Original StatusButtons Component Test</h4>
              <StatusButtons
                currentStatus="N/A"
                onStatusUpdate={handleStatusUpdate}
                isUpdating={false}
              />
            </div>

            <div className="mt-8">
              <h4 className="text-md font-semibold mb-2">Simple Test Buttons</h4>
              <button
                className="w-full h-16 bg-blue-600 text-white rounded-xl mb-2 hover:bg-blue-700"
                onClick={() => alert('Simple button works!')}
              >
                Simple Blue Button
              </button>
              <button
                className="w-full h-16 bg-green-600 text-white rounded-xl mb-2 hover:bg-green-700"
                onClick={() => alert('Green button works!')}
              >
                Simple Green Button
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ButtonTest;
