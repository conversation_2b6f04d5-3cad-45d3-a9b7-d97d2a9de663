import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FeatureToggle, UserRole } from '@/types';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import {
  Settings,
  Plus,
  Edit,
  Trash2,
  Users,
  Percent,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface FeatureToggleManagerProps {
  toggles: FeatureToggle[];
  onUpdate: (toggles: FeatureToggle[]) => void;
  canManage: boolean;
}

export const FeatureToggleManager: React.FC<FeatureToggleManagerProps> = ({
  toggles,
  onUpdate,
  canManage
}) => {
  const isMobile = useIsMobile();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedToggle, setSelectedToggle] = useState<FeatureToggle | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    enabled: false,
    rolloutPercentage: 100,
    targetRoles: [] as UserRole[],
    targetUsers: [] as string[]
  });

  const availableRoles: UserRole[] = ['berater', 'mentor', 'teamleiter', 'gebietsmanager', 'admin'];

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      enabled: false,
      rolloutPercentage: 100,
      targetRoles: [],
      targetUsers: []
    });
  };

  const handleCreate = () => {
    if (!formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    const newToggle: FeatureToggle = {
      id: Date.now().toString(),
      name: formData.name.trim(),
      description: formData.description.trim(),
      enabled: formData.enabled,
      rolloutPercentage: formData.rolloutPercentage,
      targetRoles: formData.targetRoles.length > 0 ? formData.targetRoles : undefined,
      targetUsers: formData.targetUsers.length > 0 ? formData.targetUsers : undefined,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    onUpdate([...toggles, newToggle]);
    toast.success(`Feature Toggle "${formData.name}" wurde erstellt`);
    resetForm();
    setIsCreateDialogOpen(false);
  };

  const handleEdit = () => {
    if (!selectedToggle || !formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    const updatedToggle: FeatureToggle = {
      ...selectedToggle,
      name: formData.name.trim(),
      description: formData.description.trim(),
      enabled: formData.enabled,
      rolloutPercentage: formData.rolloutPercentage,
      targetRoles: formData.targetRoles.length > 0 ? formData.targetRoles : undefined,
      targetUsers: formData.targetUsers.length > 0 ? formData.targetUsers : undefined,
      updatedAt: new Date().toISOString()
    };

    const updatedToggles = toggles.map(t => t.id === selectedToggle.id ? updatedToggle : t);
    onUpdate(updatedToggles);
    toast.success(`Feature Toggle "${formData.name}" wurde aktualisiert`);
    resetForm();
    setSelectedToggle(null);
    setIsEditDialogOpen(false);
  };

  const handleDelete = (toggle: FeatureToggle) => {
    if (!confirm(`Möchten Sie das Feature Toggle "${toggle.name}" wirklich löschen?`)) {
      return;
    }

    const updatedToggles = toggles.filter(t => t.id !== toggle.id);
    onUpdate(updatedToggles);
    toast.success(`Feature Toggle "${toggle.name}" wurde gelöscht`);
  };

  const handleToggleEnabled = (toggle: FeatureToggle) => {
    const updatedToggle = {
      ...toggle,
      enabled: !toggle.enabled,
      updatedAt: new Date().toISOString()
    };

    const updatedToggles = toggles.map(t => t.id === toggle.id ? updatedToggle : t);
    onUpdate(updatedToggles);
    toast.success(`Feature Toggle "${toggle.name}" wurde ${updatedToggle.enabled ? 'aktiviert' : 'deaktiviert'}`);
  };

  const openEditDialog = (toggle: FeatureToggle) => {
    setSelectedToggle(toggle);
    setFormData({
      name: toggle.name,
      description: toggle.description,
      enabled: toggle.enabled,
      rolloutPercentage: toggle.rolloutPercentage || 100,
      targetRoles: toggle.targetRoles || [],
      targetUsers: toggle.targetUsers || []
    });
    setIsEditDialogOpen(true);
  };

  if (!canManage) {
    return (
      <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Zugriff verweigert
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-600">
              Sie haben keine Berechtigung für die Feature-Verwaltung.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-blue-600" />
                Feature Toggle Manager
              </CardTitle>
              <CardDescription>
                Verwalten Sie Feature-Flags und Rollout-Strategien
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)} className="min-h-[44px]">
              <Plus className="h-4 w-4 mr-2" />
              Neues Feature
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {toggles.length === 0 ? (
            <div className="text-center py-8">
              <Settings className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">Noch keine Feature Toggles konfiguriert</p>
            </div>
          ) : (
            <div className="space-y-4">
              {toggles.map((toggle) => (
                <div key={toggle.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold truncate">{toggle.name}</h3>
                        <Switch
                          checked={toggle.enabled}
                          onCheckedChange={() => handleToggleEnabled(toggle)}
                        />
                        <Badge variant={toggle.enabled ? "default" : "secondary"}>
                          {toggle.enabled ? "Aktiv" : "Inaktiv"}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{toggle.description}</p>
                      
                      <div className="flex flex-wrap gap-2 text-xs">
                        {toggle.rolloutPercentage !== undefined && toggle.rolloutPercentage < 100 && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Percent className="h-3 w-3" />
                            {toggle.rolloutPercentage}% Rollout
                          </Badge>
                        )}
                        
                        {toggle.targetRoles && toggle.targetRoles.length > 0 && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {toggle.targetRoles.length} Rollen
                          </Badge>
                        )}
                        
                        {toggle.targetUsers && toggle.targetUsers.length > 0 && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {toggle.targetUsers.length} Benutzer
                          </Badge>
                        )}
                        
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(toggle.updatedAt).toLocaleDateString('de-DE')}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(toggle)}
                        className="min-h-[44px] min-w-[44px]"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(toggle)}
                        className="min-h-[44px] min-w-[44px] text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw]' : 'max-w-2xl'}`}>
          <DialogHeader>
            <DialogTitle>Neues Feature Toggle erstellen</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="feature_name"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="description">Beschreibung</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Beschreibung des Features..."
                className="mt-1"
                rows={3}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                checked={formData.enabled}
                onCheckedChange={(enabled) => setFormData(prev => ({ ...prev, enabled }))}
              />
              <Label>Feature aktiviert</Label>
            </div>
            
            <div>
              <Label htmlFor="rollout">Rollout-Prozentsatz</Label>
              <Input
                id="rollout"
                type="number"
                min="0"
                max="100"
                value={formData.rolloutPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, rolloutPercentage: parseInt(e.target.value) || 0 }))}
                className="mt-1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleCreate}>
              Erstellen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw]' : 'max-w-2xl'}`}>
          <DialogHeader>
            <DialogTitle>Feature Toggle bearbeiten</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="edit-description">Beschreibung</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="mt-1"
                rows={3}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                checked={formData.enabled}
                onCheckedChange={(enabled) => setFormData(prev => ({ ...prev, enabled }))}
              />
              <Label>Feature aktiviert</Label>
            </div>
            
            <div>
              <Label htmlFor="edit-rollout">Rollout-Prozentsatz</Label>
              <Input
                id="edit-rollout"
                type="number"
                min="0"
                max="100"
                value={formData.rolloutPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, rolloutPercentage: parseInt(e.target.value) || 0 }))}
                className="mt-1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleEdit}>
              Speichern
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
