import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/auth';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import { hasPermission } from '@/utils/roleUtils';
import {
  <PERSON>,
  Users,
  MapPin,
  Plus,
  Edit,
  Trash2,
  Map,
  BarChart3,
  Settings,
  UserPlus,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface Team {
  id: string;
  name: string;
  description?: string;
  area_id?: string;
  team_leader_id?: string;
  is_active?: boolean;
  created_at: string;
  updated_at?: string;
  areas?: {
    id: string;
    name: string;
  };
  members?: TeamMember[];
  performance?: TeamPerformance;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  joinedAt: string;
  isActive: boolean;
}

interface TeamPerformance {
  totalVisits: number;
  successRate: number;
  averageResponseTime: number;
  monthlyGrowth: number;
}

interface Area {
  id: string;
  name: string;
  description?: string;
  postal_codes?: string[];
  boundaries?: {
    type: 'Polygon';
    coordinates: number[][][];
  };
}

export const EnhancedTeamManagement: React.FC = () => {
  const { user, users } = useAuth();
  const isMobile = useIsMobile();
  
  const [teams, setTeams] = useState<Team[]>([]);
  const [areas, setAreas] = useState<Area[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPerformanceDialogOpen, setIsPerformanceDialogOpen] = useState(false);
  const [isMemberManagementOpen, setIsMemberManagementOpen] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    area_id: '',
    team_leader_id: '',
    is_active: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Mock data - in production, this would come from API
      const mockTeams: Team[] = [
        {
          id: 'team-1',
          name: 'Nord-Team',
          description: 'Verantwortlich für nördliche Gebiete',
          area_id: 'area-1',
          team_leader_id: 'leader-1',
          is_active: true,
          created_at: new Date().toISOString(),
          areas: { id: 'area-1', name: 'Hamburg Nord' },
          members: [
            {
              id: 'member-1',
              name: 'Max Mustermann',
              email: '<EMAIL>',
              role: 'berater',
              joinedAt: '2024-01-15',
              isActive: true
            },
            {
              id: 'member-2',
              name: 'Anna Schmidt',
              email: '<EMAIL>',
              role: 'mentor',
              joinedAt: '2024-02-01',
              isActive: true
            }
          ],
          performance: {
            totalVisits: 245,
            successRate: 78.5,
            averageResponseTime: 180,
            monthlyGrowth: 12.3
          }
        },
        {
          id: 'team-2',
          name: 'Süd-Team',
          description: 'Verantwortlich für südliche Gebiete',
          area_id: 'area-2',
          team_leader_id: 'leader-2',
          is_active: true,
          created_at: new Date().toISOString(),
          areas: { id: 'area-2', name: 'München Süd' },
          members: [
            {
              id: 'member-3',
              name: 'Peter Weber',
              email: '<EMAIL>',
              role: 'berater',
              joinedAt: '2024-01-20',
              isActive: true
            }
          ],
          performance: {
            totalVisits: 189,
            successRate: 82.1,
            averageResponseTime: 165,
            monthlyGrowth: 8.7
          }
        }
      ];

      const mockAreas: Area[] = [
        {
          id: 'area-1',
          name: 'Hamburg Nord',
          description: 'Nördliche Stadtteile von Hamburg',
          postal_codes: ['20095', '20099', '20144', '20146']
        },
        {
          id: 'area-2',
          name: 'München Süd',
          description: 'Südliche Stadtteile von München',
          postal_codes: ['80331', '80333', '80335', '80337']
        }
      ];

      setTeams(mockTeams);
      setAreas(mockAreas);
    } catch (error) {
      console.error('Error loading team data:', error);
      toast.error('Fehler beim Laden der Team-Daten');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTeam = async () => {
    if (!formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    try {
      const newTeam: Team = {
        id: `team-${Date.now()}`,
        name: formData.name,
        description: formData.description || undefined,
        area_id: formData.area_id || undefined,
        team_leader_id: formData.team_leader_id || undefined,
        is_active: formData.is_active,
        created_at: new Date().toISOString(),
        members: [],
        performance: {
          totalVisits: 0,
          successRate: 0,
          averageResponseTime: 0,
          monthlyGrowth: 0
        }
      };

      setTeams(prev => [...prev, newTeam]);
      toast.success(`Team "${formData.name}" wurde erstellt`);
      resetForm();
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Error creating team:', error);
      toast.error('Fehler beim Erstellen des Teams');
    }
  };

  const handleEditTeam = async () => {
    if (!selectedTeam || !formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    try {
      const updatedTeam = {
        ...selectedTeam,
        name: formData.name,
        description: formData.description || undefined,
        area_id: formData.area_id || undefined,
        team_leader_id: formData.team_leader_id || undefined,
        is_active: formData.is_active,
        updated_at: new Date().toISOString()
      };

      setTeams(prev => prev.map(t => t.id === selectedTeam.id ? updatedTeam : t));
      toast.success(`Team "${formData.name}" wurde aktualisiert`);
      resetForm();
      setSelectedTeam(null);
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating team:', error);
      toast.error('Fehler beim Aktualisieren des Teams');
    }
  };

  const handleDeleteTeam = async (team: Team) => {
    if (!confirm(`Möchten Sie das Team "${team.name}" wirklich löschen?`)) {
      return;
    }

    try {
      setTeams(prev => prev.filter(t => t.id !== team.id));
      toast.success(`Team "${team.name}" wurde gelöscht`);
    } catch (error) {
      console.error('Error deleting team:', error);
      toast.error('Fehler beim Löschen des Teams');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      area_id: '',
      team_leader_id: '',
      is_active: true
    });
  };

  const openEditDialog = (team: Team) => {
    setSelectedTeam(team);
    setFormData({
      name: team.name,
      description: team.description || '',
      area_id: team.area_id || '',
      team_leader_id: team.team_leader_id || '',
      is_active: team.is_active ?? true
    });
    setIsEditDialogOpen(true);
  };

  const teamLeaders = users.filter(u => u.role === 'teamleiter');
  const canManageTeams = user && hasPermission(user, 'team.write');

  if (!canManageTeams) {
    return (
      <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Zugriff verweigert
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-600">
              Sie haben keine Berechtigung für die Team-Verwaltung.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Team-Verwaltung</h1>
          <p className="text-gray-600">Verwalten Sie Teams und deren Gebiete</p>
        </div>
        <Button
          onClick={() => setIsCreateDialogOpen(true)}
          className="min-h-[44px]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Neues Team
        </Button>
      </div>

      {/* Teams Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {teams.map((team) => (
          <Card key={team.id} className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5 text-blue-600" />
                    {team.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {team.description || 'Keine Beschreibung'}
                  </CardDescription>
                </div>
                <Badge variant={team.is_active ? "default" : "secondary"}>
                  {team.is_active ? "Aktiv" : "Inaktiv"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Team Info */}
              <div className="space-y-2">
                {team.areas && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    {team.areas.name}
                  </div>
                )}
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="h-4 w-4" />
                  {team.members?.length || 0} Mitglieder
                </div>
              </div>

              {/* Performance Metrics */}
              {team.performance && (
                <div className="grid grid-cols-2 gap-3 pt-3 border-t">
                  <div className="text-center">
                    <p className="text-lg font-bold text-blue-600">{team.performance.totalVisits}</p>
                    <p className="text-xs text-gray-500">Besuche</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-green-600">{team.performance.successRate}%</p>
                    <p className="text-xs text-gray-500">Erfolgsrate</p>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 pt-3 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedTeam(team);
                    setIsMemberManagementOpen(true);
                  }}
                  className="flex-1 min-h-[44px]"
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Mitglieder
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedTeam(team);
                    setIsPerformanceDialogOpen(true);
                  }}
                  className="flex-1 min-h-[44px]"
                >
                  <BarChart3 className="h-4 w-4 mr-1" />
                  Leistung
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => openEditDialog(team)}
                  className="min-h-[44px] min-w-[44px]"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteTeam(team)}
                  className="min-h-[44px] min-w-[44px] text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {teams.length === 0 && !loading && (
        <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <Building className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Keine Teams vorhanden</h3>
            <p className="text-gray-600 mb-4">Erstellen Sie Ihr erstes Team, um zu beginnen.</p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Erstes Team erstellen
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Create Team Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw]' : 'max-w-2xl'}`}>
          <DialogHeader>
            <DialogTitle>Neues Team erstellen</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Team-Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="z.B. Nord-Team"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="description">Beschreibung</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Beschreibung des Teams..."
                className="mt-1"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="area">Gebiet</Label>
              <Select value={formData.area_id} onValueChange={(value) => setFormData(prev => ({ ...prev, area_id: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Gebiet auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="leader">Teamleiter</Label>
              <Select value={formData.team_leader_id} onValueChange={(value) => setFormData(prev => ({ ...prev, team_leader_id: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Teamleiter auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {teamLeaders.map(leader => (
                    <SelectItem key={leader.id} value={leader.id}>
                      {leader.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleCreateTeam}>
              Team erstellen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Team Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw]' : 'max-w-2xl'}`}>
          <DialogHeader>
            <DialogTitle>Team bearbeiten</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Team-Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="edit-description">Beschreibung</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="mt-1"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="edit-area">Gebiet</Label>
              <Select value={formData.area_id} onValueChange={(value) => setFormData(prev => ({ ...prev, area_id: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Gebiet auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="edit-leader">Teamleiter</Label>
              <Select value={formData.team_leader_id} onValueChange={(value) => setFormData(prev => ({ ...prev, team_leader_id: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Teamleiter auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {teamLeaders.map(leader => (
                    <SelectItem key={leader.id} value={leader.id}>
                      {leader.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleEditTeam}>
              Speichern
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
