
import React, { useState } from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ModernAddressForm from '@/components/address/ModernAddressForm';
import { Home, Building } from 'lucide-react';
import { useSwipe } from '@/hooks/use-swipe';
import { useSidebar } from '@/components/ui/sidebar';
import { triggerHapticFeedback } from '@/hooks/useSwipeGestures';

const IndexContent = () => {
  const { setOpenMobile } = useSidebar();
  const [activeTab, setActiveTab] = useState('efh');

  // Swipe-Handler für Sidebar öffnen und Tab-Navigation
  useSwipe({
    onSwipeRight: () => {
      setOpenMobile(true);
      triggerHapticFeedback('light');
    },
    onSwipeLeft: () => {
      // Switch between tabs
      if (activeTab === 'efh') {
        setActiveTab('mfh');
        triggerHapticFeedback('light');
      } else {
        setActiveTab('efh');
        triggerHapticFeedback('light');
      }
    }
  });

  return (
    <div className="min-h-full w-full mobile-container bg-gradient-to-br from-red-50 via-white to-red-50">
      <div className="w-full h-full">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full">
          {/* Mobile-Optimized Switch/Toggle */}
          <div className="relative bg-gradient-to-r from-red-500 via-red-600 to-red-500 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 via-transparent to-red-400/20"></div>
            <TabsList className="mobile-tabs grid-cols-2 relative bg-transparent rounded-none border-0 m-0 p-2 gap-2 h-20 md:h-24">
              <TabsTrigger
                value="efh"
                className="mobile-tab group relative py-3 px-4 md:py-4 md:px-8 rounded-2xl font-bold transition-all duration-300 transform active:scale-[0.98] data-[state=active]:bg-white data-[state=active]:text-red-600 data-[state=active]:shadow-2xl data-[state=inactive]:text-white/90 data-[state=inactive]:hover:text-white data-[state=inactive]:hover:bg-white/10 backdrop-blur-sm touch-feedback"
                data-primary="true"
              >
                <div className="flex items-center gap-2 md:gap-3">
                  <div className="p-1.5 md:p-2 rounded-xl bg-white/20 group-data-[state=active]:bg-red-500 shadow-lg transition-all duration-300">
                    <Home className="h-5 w-5 md:h-6 md:w-6 text-white group-data-[state=active]:text-white" />
                  </div>
                  <div className="text-left">
                    <div className="text-lg md:text-xl font-bold">EFH</div>
                    <div className="text-xs md:text-sm opacity-80 font-medium hidden sm:block">Einfamilienhaus</div>
                  </div>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="mfh"
                className="mobile-tab group relative py-3 px-4 md:py-4 md:px-8 rounded-2xl font-bold transition-all duration-300 transform active:scale-[0.98] data-[state=active]:bg-white data-[state=active]:text-red-600 data-[state=active]:shadow-2xl data-[state=inactive]:text-white/90 data-[state=inactive]:hover:text-white data-[state=inactive]:hover:bg-white/10 backdrop-blur-sm touch-feedback"
                data-primary="true"
              >
                <div className="flex items-center gap-2 md:gap-3">
                  <div className="p-1.5 md:p-2 rounded-xl bg-white/20 group-data-[state=active]:bg-red-500 shadow-lg transition-all duration-300">
                    <Building className="h-5 w-5 md:h-6 md:w-6 text-white group-data-[state=active]:text-white" />
                  </div>
                  <div className="text-left">
                    <div className="text-lg md:text-xl font-bold">MFH</div>
                    <div className="text-xs md:text-sm opacity-80 font-medium hidden sm:block">Mehrfamilienhaus</div>
                  </div>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* Decorative elements - hidden on very small screens */}
            <div className="absolute top-2 left-4 w-2 h-2 bg-white/30 rounded-full hidden sm:block"></div>
            <div className="absolute top-4 right-6 w-1 h-1 bg-white/40 rounded-full hidden sm:block"></div>
            <div className="absolute bottom-3 left-8 w-1.5 h-1.5 bg-white/25 rounded-full hidden sm:block"></div>
          </div>

          <TabsContent value="efh" className="p-0 m-0 animate-fade-in">
            <ModernAddressForm houseType="EFH" />
          </TabsContent>

          <TabsContent value="mfh" className="p-0 m-0 animate-fade-in">
            <ModernAddressForm houseType="MFH" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default IndexContent;
